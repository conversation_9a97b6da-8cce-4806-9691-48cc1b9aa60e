import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms, models
from PIL import Image
import os
import json
import time
import matplotlib.pyplot as plt
import numpy as np

class AgeDataset(Dataset):
    def __init__(self, data_dir, annotations_file, transform=None):
        """
        数据集类
        data_dir: 图片文件夹路径
        annotations_file: 标注文件路径 (JSON格式: {"image_name.jpg": age})
        """
        self.data_dir = data_dir
        self.transform = transform
        
        with open(annotations_file, 'r') as f:
            self.annotations = json.load(f)
        
        self.image_names = list(self.annotations.keys())
    
    def __len__(self):
        return len(self.image_names)
    
    def __getitem__(self, idx):
        img_name = self.image_names[idx]
        img_path = os.path.join(self.data_dir, img_name)
        image = Image.open(img_path).convert('RGB')
        age = float(self.annotations[img_name])
        
        if self.transform:
            image = self.transform(image)
        
        return image, age

class AgeRecognitionModel(nn.Module):
    def __init__(self, model_name='resnet50', pretrained=True):
        super(AgeRecognitionModel, self).__init__()
        
        # 选择基础模型
        if model_name == 'resnet18':
            self.backbone = models.resnet18(pretrained=pretrained)
            feature_dim = self.backbone.fc.in_features
        elif model_name == 'resnet34':
            self.backbone = models.resnet34(pretrained=pretrained)
            feature_dim = self.backbone.fc.in_features
        elif model_name == 'resnet50':
            self.backbone = models.resnet50(pretrained=pretrained)
            feature_dim = self.backbone.fc.in_features
        elif model_name == 'efficientnet_b0':
            self.backbone = models.efficientnet_b0(pretrained=pretrained)
            feature_dim = self.backbone.classifier[1].in_features
        else:
            raise ValueError(f"不支持的模型: {model_name}")
        
        # 移除原始分类层
        if 'resnet' in model_name:
            self.backbone = nn.Sequential(*list(self.backbone.children())[:-1])
        elif 'efficientnet' in model_name:
            self.backbone = self.backbone.features
        
        # 添加年龄回归层
        self.age_regressor = nn.Sequential(
            nn.Flatten(),
            nn.Linear(feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 1)
        )
    
    def forward(self, x):
        features = self.backbone(x)
        age = self.age_regressor(features)
        return age

class AgeRecognitionTrainer:
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu', 
                 learning_rate=0.0001, weight_decay=0.0001):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.MSELoss()
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', factor=0.5, patience=3)
        
        self.train_losses = []
        self.val_losses = []
        self.val_maes = []
    
    def train_epoch(self, dataloader):
        self.model.train()
        total_loss = 0
        num_batches = len(dataloader)
        
        for batch_idx, (images, ages) in enumerate(dataloader):
            images, ages = images.to(self.device), ages.to(self.device).float().unsqueeze(1)
            
            self.optimizer.zero_grad()
            outputs = self.model(images)
            loss = self.criterion(outputs, ages)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            
            # 显示批次进度
            if (batch_idx + 1) % max(1, num_batches // 5) == 0:
                print(f'  批次 [{batch_idx+1}/{num_batches}] - 损失: {loss.item():.4f}')
        
        return total_loss / len(dataloader)
    
    def validate(self, dataloader):
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_samples = 0
        
        with torch.no_grad():
            for images, ages in dataloader:
                images, ages = images.to(self.device), ages.to(self.device).float().unsqueeze(1)
                outputs = self.model(images)
                loss = self.criterion(outputs, ages)
                
                # 计算平均绝对误差
                mae = torch.abs(outputs - ages).sum().item()
                
                total_loss += loss.item()
                total_mae += mae
                num_samples += ages.size(0)
        
        avg_mae = total_mae / num_samples
        return total_loss / len(dataloader), avg_mae
    
    def train(self, train_loader, val_loader, epochs=50, early_stopping_patience=10):
        best_val_loss = float('inf')
        best_val_mae = float('inf')
        patience_counter = 0
        
        print(f"开始训练 - 设备: {self.device}")
        print(f"训练批次数: {len(train_loader)}, 验证批次数: {len(val_loader)}")
        print("-" * 60)
        
        start_time = time.time()
        
        for epoch in range(epochs):
            epoch_start = time.time()
            print(f'\n轮次 {epoch+1}/{epochs}:')
            print('训练中...')
            
            train_loss = self.train_epoch(train_loader)
            self.train_losses.append(train_loss)
            
            print('验证中...')
            val_loss, val_mae = self.validate(val_loader)
            self.val_losses.append(val_loss)
            self.val_maes.append(val_mae)
            
            # 更新学习率
            self.scheduler.step(val_loss)
            current_lr = self.optimizer.param_groups[0]['lr']
            
            epoch_time = time.time() - epoch_start
            
            print(f'训练损失: {train_loss:.4f} | 验证损失: {val_loss:.4f} | 平均误差: {val_mae:.2f}岁')
            print(f'学习率: {current_lr:.6f} | 用时: {epoch_time:.1f}秒')
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), 'best_age_model.pth')
                print('✓ 保存最佳模型 (最低损失)')
                patience_counter = 0
            else:
                patience_counter += 1
            
            # 保存最低MAE模型
            if val_mae < best_val_mae:
                best_val_mae = val_mae
                torch.save(self.model.state_dict(), 'best_mae_age_model.pth')
                print('✓ 保存最佳模型 (最低MAE)')
            
            print("-" * 60)
            
            # 早停
            if patience_counter >= early_stopping_patience:
                print(f'早停: {early_stopping_patience} 轮次内验证损失未改善')
                break
        
        total_time = time.time() - start_time
        print(f'\n训练完成! 总用时: {total_time/60:.1f}分钟')
        print(f'最佳验证损失: {best_val_loss:.4f}, 最佳平均误差: {best_val_mae:.2f}岁')
        
        # 绘制训练曲线
        self.plot_training_curves()
        
        return best_val_loss, best_val_mae
    
    def plot_training_curves(self):
        plt.figure(figsize=(12, 5))
        
        # 损失曲线
        plt.subplot(1, 2, 1)
        plt.plot(self.train_losses, label='训练损失')
        plt.plot(self.val_losses, label='验证损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.title('训练和验证损失')
        plt.legend()
        plt.grid(True)
        
        # MAE曲线
        plt.subplot(1, 2, 2)
        plt.plot(self.val_maes, label='验证MAE', color='green')
        plt.xlabel('轮次')
        plt.ylabel('平均绝对误差 (岁)')
        plt.title('验证集平均绝对误差')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('age_training_curves.png')
        print("训练曲线已保存为 'age_training_curves.png'")
    
    def predict(self, image_path):
        self.model.eval()
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        image = Image.open(image_path).convert('RGB')
        image = transform(image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            age = self.model(image).item()
        
        return max(0, age)  # 确保年龄非负

def main():
    print("=" * 60)
    print("MegaAsian-Age 年龄识别模型训练程序")
    print("=" * 60)
    
    # 检查数据集是否存在
    if not os.path.exists('data/train_annotations.json'):
        print("数据集不存在，请先运行: python process_megaasian_age.py")
        return
    
    print("数据集检查通过")
    
    # 数据预处理
    print("准备数据预处理...")
    train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    print("加载数据集...")
    train_dataset = AgeDataset('data/train', 'data/train_annotations.json', train_transform)
    val_dataset = AgeDataset('data/val', 'data/val_annotations.json', val_transform)
    
    print(f"训练集大小: {len(train_dataset)} 张图片")
    print(f"验证集大小: {len(val_dataset)} 张图片")
    
    if len(train_dataset) == 0 or len(val_dataset) == 0:
        print("数据集为空，请检查数据")
        return
    
    # 创建数据加载器
    batch_size = 32
    if torch.cuda.is_available():
        # 根据GPU内存调整批次大小
        gpu_mem = torch.cuda.get_device_properties(0).total_memory / 1e9  # GB
        if gpu_mem < 4:
            batch_size = 8
        elif gpu_mem < 8:
            batch_size = 16
    else:
        # CPU模式使用较小批次
        batch_size = 8
    
    num_workers = 4 if os.name != 'nt' else 0  # Windows下设为0
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=num_workers)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)
    
    print(f"批次大小: {batch_size}, 工作进程: {num_workers}")
    
    # 选择模型
    model_name = 'resnet50'  # 可选: 'resnet18', 'resnet34', 'resnet50', 'efficientnet_b0'
    print(f"初始化模型: {model_name}...")
    model = AgeRecognitionModel(model_name=model_name, pretrained=True)
    
    # 创建训练器
    trainer = AgeRecognitionTrainer(model, learning_rate=0.0001)
    
    # 显示模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数总数: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    
    # 训练模型
    epochs = 30
    trainer.train(train_loader, val_loader, epochs=epochs, early_stopping_patience=10)
    
    # 测试预测
    print("\n测试预测效果:")
    for i in range(min(5, len(val_dataset))):
        test_image_path = os.path.join('data/val', val_dataset.image_names[i])
        predicted_age = trainer.predict(test_image_path)
        actual_age = val_dataset.annotations[val_dataset.image_names[i]]
        error = abs(predicted_age - actual_age)
        print(f"  样本 {i+1}: 实际 {actual_age}岁 -> 预测 {predicted_age:.1f}岁 (误差: {error:.1f}岁)")
    
    print("\n训练完成! 模型已保存为 'best_age_model.pth' 和 'best_mae_age_model.pth'")

if __name__ == '__main__':
    main()