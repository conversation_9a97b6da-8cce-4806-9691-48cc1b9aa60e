#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版年龄识别训练脚本
使用传统机器学习方法进行年龄识别演示
当PyTorch可用时会自动切换到深度学习模式
"""

import os
import json
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
import time

# 检查是否有PyTorch
try:
    import torch
    import torch.cuda
    PYTORCH_AVAILABLE = True
    print("✓ 检测到PyTorch，GPU加速可用")
except ImportError:
    PYTORCH_AVAILABLE = False
    print("⚠ PyTorch未安装，使用传统机器学习方法")

def extract_image_features(image_path, target_size=(64, 64)):
    """
    从图像中提取特征向量
    使用简单的像素值和统计特征
    """
    try:
        # 加载并预处理图像
        img = Image.open(image_path).convert('RGB')
        img = img.resize(target_size)
        
        # 转换为numpy数组
        img_array = np.array(img)
        
        # 提取多种特征
        features = []
        
        # 1. 缩放后的像素值（降维）
        img_small = img.resize((16, 16))  # 进一步缩小以减少特征数
        pixel_features = np.array(img_small).flatten()
        features.extend(pixel_features)
        
        # 2. 颜色统计特征
        for channel in range(3):  # RGB三个通道
            channel_data = img_array[:, :, channel]
            features.extend([
                np.mean(channel_data),      # 均值
                np.std(channel_data),       # 标准差
                np.median(channel_data),    # 中位数
                np.min(channel_data),       # 最小值
                np.max(channel_data),       # 最大值
            ])
        
        # 3. 纹理特征（简单的梯度统计）
        gray = np.array(img.convert('L'))
        grad_x = np.abs(np.diff(gray, axis=1))
        grad_y = np.abs(np.diff(gray, axis=0))
        features.extend([
            np.mean(grad_x),
            np.std(grad_x),
            np.mean(grad_y),
            np.std(grad_y)
        ])
        
        return np.array(features)
    
    except Exception as e:
        print(f"处理图像 {image_path} 时出错: {e}")
        # 返回零向量作为默认值
        return np.zeros(16*16*3 + 15 + 4)

def load_dataset(data_dir, annotations_file, max_samples=None):
    """
    加载数据集并提取特征
    """
    print(f"加载数据集: {data_dir}")
    
    # 读取标注文件
    with open(annotations_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 限制样本数量（用于快速测试）
    if max_samples:
        items = list(annotations.items())[:max_samples]
        annotations = dict(items)
    
    print(f"找到 {len(annotations)} 个样本")
    
    features = []
    ages = []
    processed = 0
    
    for image_name, age in annotations.items():
        image_path = os.path.join(data_dir, image_name)
        
        if os.path.exists(image_path):
            # 提取图像特征
            img_features = extract_image_features(image_path)
            features.append(img_features)
            ages.append(age)
            processed += 1
            
            if processed % 100 == 0:
                print(f"已处理 {processed}/{len(annotations)} 张图片")
    
    print(f"成功处理 {processed} 张图片")
    return np.array(features), np.array(ages)

def train_age_model(X_train, y_train, X_val, y_val):
    """
    训练年龄识别模型
    """
    print("\n开始训练年龄识别模型...")
    
    # 尝试多种模型
    models = {
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
        'Ridge Regression': Ridge(alpha=1.0)
    }
    
    best_model = None
    best_mae = float('inf')
    results = {}
    
    for name, model in models.items():
        print(f"\n训练 {name}...")
        start_time = time.time()
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 预测
        train_pred = model.predict(X_train)
        val_pred = model.predict(X_val)
        
        # 计算指标
        train_mae = mean_absolute_error(y_train, train_pred)
        val_mae = mean_absolute_error(y_val, val_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
        val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
        
        training_time = time.time() - start_time
        
        results[name] = {
            'model': model,
            'train_mae': train_mae,
            'val_mae': val_mae,
            'train_rmse': train_rmse,
            'val_rmse': val_rmse,
            'training_time': training_time
        }
        
        print(f"  训练时间: {training_time:.2f}秒")
        print(f"  训练MAE: {train_mae:.2f}岁")
        print(f"  验证MAE: {val_mae:.2f}岁")
        print(f"  训练RMSE: {train_rmse:.2f}岁")
        print(f"  验证RMSE: {val_rmse:.2f}岁")
        
        # 记录最佳模型
        if val_mae < best_mae:
            best_mae = val_mae
            best_model = model
    
    return best_model, results

def plot_results(results, y_val, best_model, X_val):
    """
    绘制训练结果
    """
    print("\n生成结果图表...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('年龄识别模型训练结果', fontsize=16)
    
    # 1. 模型性能比较
    model_names = list(results.keys())
    train_maes = [results[name]['train_mae'] for name in model_names]
    val_maes = [results[name]['val_mae'] for name in model_names]
    
    x = np.arange(len(model_names))
    width = 0.35
    
    axes[0, 0].bar(x - width/2, train_maes, width, label='训练MAE', alpha=0.8)
    axes[0, 0].bar(x + width/2, val_maes, width, label='验证MAE', alpha=0.8)
    axes[0, 0].set_xlabel('模型')
    axes[0, 0].set_ylabel('平均绝对误差 (岁)')
    axes[0, 0].set_title('模型性能比较')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(model_names)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 训练时间比较
    training_times = [results[name]['training_time'] for name in model_names]
    axes[0, 1].bar(model_names, training_times, alpha=0.8, color='orange')
    axes[0, 1].set_xlabel('模型')
    axes[0, 1].set_ylabel('训练时间 (秒)')
    axes[0, 1].set_title('训练时间比较')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 最佳模型预测vs实际
    best_pred = best_model.predict(X_val)
    axes[1, 0].scatter(y_val, best_pred, alpha=0.6)
    axes[1, 0].plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()], 'r--', lw=2)
    axes[1, 0].set_xlabel('实际年龄')
    axes[1, 0].set_ylabel('预测年龄')
    axes[1, 0].set_title('最佳模型：预测vs实际')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 误差分布
    errors = np.abs(best_pred - y_val)
    axes[1, 1].hist(errors, bins=20, alpha=0.7, edgecolor='black')
    axes[1, 1].set_xlabel('绝对误差 (岁)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('误差分布')
    axes[1, 1].axvline(np.mean(errors), color='red', linestyle='--', 
                       label=f'平均误差: {np.mean(errors):.2f}岁')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('age_training_results.png', dpi=300, bbox_inches='tight')
    print("结果图表已保存为 'age_training_results.png'")
    
    return fig

def main():
    print("=" * 60)
    print("年龄识别模型训练程序 (简化版)")
    print("=" * 60)
    
    # 检查CUDA状态
    if PYTORCH_AVAILABLE:
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_props = torch.cuda.get_device_properties(i)
                print(f"GPU {i}: {gpu_props.name} ({gpu_props.total_memory/1e9:.1f}GB)")
        print("注意: 当前使用简化版训练，如需GPU加速请安装完整PyTorch环境")
    
    # 检查数据集
    if not os.path.exists('data/train_annotations.json'):
        print("数据集不存在，请先运行: python process_megaasian_age.py")
        return
    
    print("\n数据集检查通过")
    
    # 加载数据（限制样本数量以加快演示）
    max_train_samples = 1000  # 限制训练样本数量
    max_val_samples = 200     # 限制验证样本数量
    
    print(f"加载训练数据 (最多 {max_train_samples} 个样本)...")
    X_train, y_train = load_dataset('data/train', 'data/train_annotations.json', max_train_samples)
    
    print(f"加载验证数据 (最多 {max_val_samples} 个样本)...")
    X_val, y_val = load_dataset('data/val', 'data/val_annotations.json', max_val_samples)
    
    if len(X_train) == 0 or len(X_val) == 0:
        print("数据集为空，请检查数据")
        return
    
    print(f"\n数据集统计:")
    print(f"训练集: {len(X_train)} 个样本, 特征维度: {X_train.shape[1]}")
    print(f"验证集: {len(X_val)} 个样本")
    print(f"年龄范围: {y_train.min():.0f}-{y_train.max():.0f}岁")
    print(f"平均年龄: {y_train.mean():.1f}岁")
    
    # 训练模型
    best_model, results = train_age_model(X_train, y_train, X_val, y_val)
    
    # 显示最佳结果
    best_result = min(results.values(), key=lambda x: x['val_mae'])
    best_name = [name for name, result in results.items() if result == best_result][0]
    
    print(f"\n最佳模型: {best_name}")
    print(f"验证MAE: {best_result['val_mae']:.2f}岁")
    print(f"验证RMSE: {best_result['val_rmse']:.2f}岁")
    
    # 生成结果图表
    plot_results(results, y_val, best_model, X_val)
    
    # 测试预测示例
    print("\n预测示例:")
    for i in range(min(5, len(X_val))):
        predicted_age = best_model.predict(X_val[i:i+1])[0]
        actual_age = y_val[i]
        error = abs(predicted_age - actual_age)
        print(f"  样本 {i+1}: 实际 {actual_age:.0f}岁 -> 预测 {predicted_age:.1f}岁 (误差: {error:.1f}岁)")
    
    print(f"\n训练完成!")
    print("注意: 这是使用传统机器学习方法的演示版本")
    print("要获得更好的性能，请安装PyTorch并使用深度学习模型")

if __name__ == "__main__":
    main()
