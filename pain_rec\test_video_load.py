#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频加载和基本信息
"""

import cv2
import os

def test_video_info(video_path):
    """测试视频文件信息"""
    print(f"🔍 测试视频文件: {video_path}")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 文件不存在: {video_path}")
        return False
    
    print(f"✅ 文件存在")
    print(f"   文件大小: {os.path.getsize(video_path) / 1024 / 1024:.1f} MB")
    
    # 尝试打开视频
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"❌ 无法打开视频文件")
        return False
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps if fps > 0 else 0
    
    print(f"✅ 视频信息:")
    print(f"   分辨率: {width} x {height}")
    print(f"   帧率: {fps:.2f} FPS")
    print(f"   总帧数: {frame_count}")
    print(f"   时长: {duration:.2f} 秒")
    
    # 尝试读取第一帧
    ret, frame = cap.read()
    if ret:
        print(f"✅ 成功读取第一帧")
        print(f"   帧尺寸: {frame.shape}")
    else:
        print(f"❌ 无法读取第一帧")
    
    cap.release()
    return True

def test_model_loading():
    """测试模型加载"""
    print(f"\n🧠 测试模型加载...")
    
    try:
        import torch
        print(f"✅ PyTorch导入成功")
        print(f"   版本: {torch.__version__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        
        # 测试模型文件
        model_path = 'best_optimized_age_model.pth'
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return False
        
        print(f"✅ 模型文件存在: {model_path}")
        print(f"   文件大小: {os.path.getsize(model_path) / 1024 / 1024:.1f} MB")
        
        # 尝试加载模型
        model_data = torch.load(model_path, map_location='cpu')
        print(f"✅ 模型加载成功")
        print(f"   数据类型: {type(model_data)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_face_detection():
    """测试人脸检测器"""
    print(f"\n👤 测试人脸检测器...")
    
    try:
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        if face_cascade.empty():
            print(f"❌ 人脸检测器加载失败")
            return False
        
        print(f"✅ 人脸检测器加载成功")
        return True
        
    except Exception as e:
        print(f"❌ 人脸检测器错误: {e}")
        return False

def main():
    print("=" * 60)
    print("🧪 视频年龄识别系统测试")
    print("=" * 60)
    
    # 测试视频文件
    video_path = "年龄识别.mp4"
    video_ok = test_video_info(video_path)
    
    # 测试模型加载
    model_ok = test_model_loading()
    
    # 测试人脸检测
    face_ok = test_face_detection()
    
    print(f"\n📊 测试结果:")
    print(f"   视频文件: {'✅' if video_ok else '❌'}")
    print(f"   模型加载: {'✅' if model_ok else '❌'}")
    print(f"   人脸检测: {'✅' if face_ok else '❌'}")
    
    if video_ok and model_ok and face_ok:
        print(f"\n🎉 所有测试通过，可以开始年龄识别!")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关组件")

if __name__ == "__main__":
    main()
