# 年龄识别模型训练指南

本项目基于MegaAsian-Age数据集训练一个年龄识别模型，可以从人脸图像中预测年龄。

## 环境要求

项目使用的Python环境已在`environment.yml`中定义，包括：
- Python 3.9
- PyTorch >= 1.9.0
- torchvision >= 0.10.0
- scikit-learn >= 1.0.0
- PIL >= 8.0.0
- matplotlib (用于绘制训练曲线)

## 数据集准备

1. 首先，确保您已下载MegaAsian-Age数据集并解压到项目目录下的`megaasian_age`文件夹中。
   数据集应包含以下结构：
   ```
   megaasian_age/
   ├── train/         # 训练图片
   ├── test/          # 测试图片
   └── list/          # 标签文件
       ├── train_name.txt  # 训练图片名称
       └── train_age.txt   # 对应的年龄标签
   ```

2. 运行数据集处理脚本，将数据集分割为训练集和验证集：
   ```
   python process_megaasian_age.py
   ```
   
   这将创建以下目录和文件：
   - `data/train/` - 训练图片
   - `data/val/` - 验证图片
   - `data/train_annotations.json` - 训练集标注
   - `data/val_annotations.json` - 验证集标注

## 模型训练

运行训练脚本开始训练模型：
```
python train_age_model.py
```

训练过程中会显示每个epoch的训练损失、验证损失和平均绝对误差(MAE)。训练完成后，会生成：
- `best_age_model.pth` - 验证损失最低的模型
- `best_mae_age_model.pth` - 平均绝对误差最低的模型
- `age_training_curves.png` - 训练曲线图

## 模型预测

使用训练好的模型进行年龄预测：
```
python predict_age.py --image <图片路径> [--model <模型路径>] [--model_name <模型架构>]
```

参数说明：
- `--image`: 要预测的图片路径（必需）
- `--model`: 模型文件路径，默认为`best_age_model.pth`
- `--model_name`: 模型架构，可选值为`resnet18`、`resnet34`、`resnet50`、`efficientnet_b0`，默认为`resnet50`

示例：
```
python predict_age.py --image test_image.jpg --model best_mae_age_model.pth
```

## 模型架构

默认使用ResNet50作为基础模型，您可以在训练时通过修改`train_age_model.py`中的`model_name`参数来选择不同的模型架构：
- `resnet18` - 较小的模型，适合资源有限的环境
- `resnet34` - 中等大小的模型
- `resnet50` - 默认模型，平衡性能和准确度
- `efficientnet_b0` - 高效模型，可能有更好的性能

## 性能优化

如果训练速度较慢，可以尝试以下优化方法：
1. 减小批次大小（batch_size）
2. 使用更小的模型架构（如resnet18）
3. 减少训练轮次（epochs）
4. 如果有GPU，确保使用CUDA加速

## 注意事项

- 训练时间取决于数据集大小和硬件性能
- 对于大型数据集，建议使用GPU进行训练
- 模型预测的年龄是连续值，可能与实际年龄有一定误差