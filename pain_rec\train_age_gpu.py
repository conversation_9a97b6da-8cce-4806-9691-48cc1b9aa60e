#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU加速年龄识别训练脚本
避免torchvision.models兼容性问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import torchvision.transforms as transforms
import os
import json
import time
import matplotlib.pyplot as plt
import numpy as np

class AgeDataset(Dataset):
    def __init__(self, data_dir, annotations_file, transform=None):
        """
        数据集类
        data_dir: 图片文件夹路径
        annotations_file: 标注文件路径 (JSON格式: {"image_name.jpg": age})
        """
        self.data_dir = data_dir
        self.transform = transform
        
        # 读取标注文件
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        self.image_names = list(self.annotations.keys())
        print(f"加载了 {len(self.image_names)} 张图片")
    
    def __len__(self):
        return len(self.image_names)
    
    def __getitem__(self, idx):
        image_name = self.image_names[idx]
        image_path = os.path.join(self.data_dir, image_name)
        age = self.annotations[image_name]
        
        # 加载图片
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image, age

class SimpleCNN(nn.Module):
    def __init__(self):
        super(SimpleCNN, self).__init__()
        
        # 特征提取层
        self.features = nn.Sequential(
            # 第一个卷积块 (224x224x3 -> 112x112x64)
            nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
            
            # 第二个卷积块 (56x56x64 -> 28x28x128)
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 第三个卷积块 (28x28x128 -> 14x14x256)
            nn.Conv2d(128, 256, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 第四个卷积块 (14x14x256 -> 7x7x512)
            nn.Conv2d(256, 512, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 全局平均池化 (7x7x512 -> 1x1x512)
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 1)  # 输出年龄值
        )
    
    def forward(self, x):
        x = self.features(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x.squeeze()

class AgeRecognitionTrainer:
    def __init__(self, model, learning_rate=0.001, device=None):
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
            
        self.model = model.to(self.device)
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', patience=5, factor=0.5)
        self.criterion = nn.MSELoss()
        
        self.train_losses = []
        self.val_losses = []
        self.val_maes = []
        
        print(f"使用设备: {self.device}")
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB")
    
    def train_epoch(self, train_loader):
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (images, ages) in enumerate(train_loader):
            images = images.to(self.device)
            ages = ages.float().to(self.device)
            
            self.optimizer.zero_grad()
            outputs = self.model(images)
            loss = self.criterion(outputs, ages)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 10 == 0:
                print(f'  批次 {batch_idx}/{len(train_loader)}, 损失: {loss.item():.4f}')
        
        return total_loss / num_batches
    
    def validate(self, val_loader):
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_samples = 0
        
        with torch.no_grad():
            for images, ages in val_loader:
                images = images.to(self.device)
                ages = ages.float().to(self.device)
                
                outputs = self.model(images)
                loss = self.criterion(outputs, ages)
                mae = torch.mean(torch.abs(outputs - ages))
                
                total_loss += loss.item() * images.size(0)
                total_mae += mae.item() * images.size(0)
                num_samples += images.size(0)
        
        avg_loss = total_loss / num_samples
        avg_mae = total_mae / num_samples
        
        return avg_loss, avg_mae
    
    def train(self, train_loader, val_loader, epochs=50, early_stopping_patience=10):
        print(f"开始训练 {epochs} 个轮次...")
        
        best_val_loss = float('inf')
        best_mae = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            print(f"\n轮次 {epoch+1}/{epochs}")
            print("-" * 50)
            
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_mae = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_maes.append(val_mae)
            
            epoch_time = time.time() - start_time
            
            print(f"训练损失: {train_loss:.4f}")
            print(f"验证损失: {val_loss:.4f}")
            print(f"验证MAE: {val_mae:.2f}岁")
            print(f"学习率: {self.optimizer.param_groups[0]['lr']:.6f}")
            print(f"耗时: {epoch_time:.1f}秒")
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), 'best_age_model_gpu.pth')
                print("✓ 保存最佳损失模型")
                patience_counter = 0
            else:
                patience_counter += 1
            
            if val_mae < best_mae:
                best_mae = val_mae
                torch.save(self.model.state_dict(), 'best_mae_age_model_gpu.pth')
                print("✓ 保存最佳MAE模型")
            
            # 早停检查
            if patience_counter >= early_stopping_patience:
                print(f"\n早停触发！验证损失连续 {early_stopping_patience} 轮次未改善")
                break
            
            # GPU内存状态
            if torch.cuda.is_available():
                print(f"GPU内存: {torch.cuda.memory_allocated()/1e9:.2f}GB / {torch.cuda.memory_reserved()/1e9:.2f}GB")
        
        print(f"\n训练完成！最佳验证损失: {best_val_loss:.4f}, 最佳MAE: {best_mae:.2f}岁")
        self.plot_training_curves()
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        plt.figure(figsize=(15, 5))
        
        # 损失曲线
        plt.subplot(1, 3, 1)
        plt.plot(self.train_losses, label='训练损失')
        plt.plot(self.val_losses, label='验证损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.title('训练和验证损失')
        plt.legend()
        plt.grid(True)
        
        # MAE曲线
        plt.subplot(1, 3, 2)
        plt.plot(self.val_maes, label='验证MAE', color='orange')
        plt.xlabel('轮次')
        plt.ylabel('平均绝对误差 (岁)')
        plt.title('验证MAE')
        plt.legend()
        plt.grid(True)
        
        # 学习率曲线
        plt.subplot(1, 3, 3)
        lrs = [self.optimizer.param_groups[0]['lr']] * len(self.train_losses)
        plt.plot(lrs, label='学习率')
        plt.xlabel('轮次')
        plt.ylabel('学习率')
        plt.title('学习率变化')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('age_training_curves_gpu.png', dpi=300, bbox_inches='tight')
        print("训练曲线已保存为 'age_training_curves_gpu.png'")
    
    def predict(self, image_path):
        """预测单张图片的年龄"""
        self.model.eval()
        
        # 预处理
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        image = Image.open(image_path).convert('RGB')
        image = transform(image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            age = self.model(image).item()
        
        return max(0, age)  # 确保年龄非负

def main():
    print("=" * 60)
    print("GPU加速年龄识别模型训练程序")
    print("=" * 60)
    
    # 检查CUDA状态
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_props = torch.cuda.get_device_properties(i)
            print(f"GPU {i}: {gpu_props.name} ({gpu_props.total_memory/1e9:.1f}GB)")
    else:
        print("警告: CUDA不可用，将使用CPU训练（速度较慢）")
    
    # 检查数据集
    if not os.path.exists('data/train_annotations.json'):
        print("数据集不存在，请先运行: python process_megaasian_age.py")
        return
    
    print("\n数据集检查通过")
    
    # 数据预处理
    print("准备数据预处理...")
    train_transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.RandomCrop((224, 224)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(15),
        transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.2, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    print("加载数据集...")
    train_dataset = AgeDataset('data/train', 'data/train_annotations.json', train_transform)
    val_dataset = AgeDataset('data/val', 'data/val_annotations.json', val_transform)
    
    print(f"训练集大小: {len(train_dataset)} 张图片")
    print(f"验证集大小: {len(val_dataset)} 张图片")
    
    if len(train_dataset) == 0 or len(val_dataset) == 0:
        print("数据集为空，请检查数据")
        return
    
    # 智能批次大小配置 - 优化GPU内存使用
    batch_size = 32
    if torch.cuda.is_available():
        gpu_mem = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"检测到 {gpu_mem:.1f}GB GPU内存")

        # 更激进的批次大小配置以充分利用GPU内存
        if gpu_mem >= 12:  # RTX 4070 SUPER有12.9GB
            batch_size = 128  # 增加到128
            print(f"大内存GPU，设置批次大小为: {batch_size}")
        elif gpu_mem >= 8:
            batch_size = 96
            print(f"中等内存GPU，设置批次大小为: {batch_size}")
        elif gpu_mem >= 6:
            batch_size = 64
            print(f"标准内存GPU，设置批次大小为: {batch_size}")
        else:
            batch_size = 32
            print(f"小内存GPU，设置批次大小为: {batch_size}")
    else:
        batch_size = 8
        print("CPU模式，使用较小批次大小")
    
    # 数据加载器
    num_workers = 0 if os.name == 'nt' else min(4, os.cpu_count())
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, 
                             num_workers=num_workers, pin_memory=torch.cuda.is_available())
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, 
                           num_workers=num_workers, pin_memory=torch.cuda.is_available())
    
    print(f"批次大小: {batch_size}, 工作进程: {num_workers}")
    
    # 创建模型
    print("初始化模型...")
    model = SimpleCNN()
    
    # 显示模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数总数: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    
    # 创建训练器
    learning_rate = 0.001 if torch.cuda.is_available() else 0.0001
    trainer = AgeRecognitionTrainer(model, learning_rate=learning_rate)
    
    # 训练模型
    epochs = 50 if torch.cuda.is_available() else 20
    print(f"开始训练 {epochs} 个轮次...")
    
    trainer.train(train_loader, val_loader, epochs=epochs, early_stopping_patience=15)
    
    # 测试预测
    print("\n测试预测效果:")
    for i in range(min(5, len(val_dataset))):
        test_image_path = os.path.join('data/val', val_dataset.image_names[i])
        predicted_age = trainer.predict(test_image_path)
        actual_age = val_dataset.annotations[val_dataset.image_names[i]]
        error = abs(predicted_age - actual_age)
        print(f"  样本 {i+1}: 实际 {actual_age}岁 -> 预测 {predicted_age:.1f}岁 (误差: {error:.1f}岁)")
    
    print("\n训练完成! 模型已保存为 'best_age_model_gpu.pth' 和 'best_mae_age_model_gpu.pth'")
    print("训练曲线图已保存为 'age_training_curves_gpu.png'")
    
    # 显示GPU使用情况
    if torch.cuda.is_available():
        print(f"\nGPU内存使用情况:")
        print(f"已分配: {torch.cuda.memory_allocated()/1e9:.2f}GB")
        print(f"缓存: {torch.cuda.memory_reserved()/1e9:.2f}GB")

if __name__ == "__main__":
    main()
