#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频年龄识别测试脚本
用于测试和验证视频年龄识别功能
"""

import cv2
import torch
import numpy as np
import os
import argparse
from PIL import Image
import time

def test_camera_access():
    """测试摄像头访问"""
    print("🔍 测试摄像头访问...")
    
    for camera_id in range(3):  # 测试前3个摄像头ID
        cap = cv2.VideoCapture(camera_id)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                h, w = frame.shape[:2]
                print(f"   ✅ 摄像头 {camera_id}: {w}x{h}")
                cap.release()
                return camera_id
            cap.release()
        else:
            print(f"   ❌ 摄像头 {camera_id}: 无法访问")
    
    print("   ⚠️ 未找到可用摄像头")
    return None

def test_face_detection():
    """测试人脸检测功能"""
    print("\n🔍 测试人脸检测功能...")
    
    # 加载人脸检测器
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    if face_cascade.empty():
        print("   ❌ 人脸检测器加载失败")
        return False
    
    print("   ✅ 人脸检测器加载成功")
    
    # 测试摄像头人脸检测
    camera_id = test_camera_access()
    if camera_id is not None:
        print(f"   🎥 使用摄像头 {camera_id} 测试人脸检测...")
        
        cap = cv2.VideoCapture(camera_id)
        start_time = time.time()
        face_detected = False
        
        while time.time() - start_time < 10:  # 测试10秒
            ret, frame = cap.read()
            if not ret:
                break
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 5, minSize=(30, 30))
            
            # 绘制人脸框
            for (x, y, w, h) in faces:
                cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                face_detected = True
            
            # 显示状态
            status = f"Faces: {len(faces)} | Press 'q' to quit"
            cv2.putText(frame, status, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            cv2.imshow('Face Detection Test', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        if face_detected:
            print("   ✅ 人脸检测功能正常")
        else:
            print("   ⚠️ 未检测到人脸 (可能是光线或角度问题)")
        
        return True
    
    return False

def test_model_loading():
    """测试模型加载"""
    print("\n🔍 测试模型加载...")
    
    # 查找可用的模型文件
    model_paths = [
        'checkpoints/best_model.pth',
        'checkpoints/latest_checkpoint.pth',
        'best_optimized_age_model.pth',
        'best_age_model_gpu.pth'
    ]
    
    available_models = []
    for model_path in model_paths:
        if os.path.exists(model_path):
            available_models.append(model_path)
            print(f"   ✅ 找到模型: {model_path}")
    
    if not available_models:
        print("   ❌ 未找到训练好的模型文件")
        print("   请先完成模型训练")
        return None
    
    # 测试加载第一个可用模型
    model_path = available_models[0]
    print(f"   🧠 测试加载模型: {model_path}")
    
    try:
        # 尝试加载模型
        checkpoint = torch.load(model_path, map_location='cpu')
        
        if 'model_state_dict' in checkpoint:
            print(f"   ✅ 检查点格式正确")
            print(f"   📊 轮次: {checkpoint.get('epoch', '未知')}")
            print(f"   🎯 MAE: {checkpoint.get('val_mae', '未知'):.3f}岁")
        else:
            print(f"   ✅ 模型权重格式正确")
        
        return model_path
        
    except Exception as e:
        print(f"   ❌ 模型加载失败: {e}")
        return None

def test_pytorch_environment():
    """测试PyTorch环境"""
    print("\n🔍 测试PyTorch环境...")
    
    try:
        import torch
        import torchvision
        
        print(f"   ✅ PyTorch版本: {torch.__version__}")
        print(f"   ✅ torchvision版本: {torchvision.__version__}")
        print(f"   🎮 CUDA可用: {'✅' if torch.cuda.is_available() else '❌'}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"   🎮 GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        
        # 测试简单的张量操作
        x = torch.randn(3, 224, 224)
        print(f"   ✅ 张量操作正常: {x.shape}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ PyTorch导入失败: {e}")
        return False

def create_test_video():
    """创建测试视频"""
    print("\n🎬 创建测试视频...")
    
    # 创建一个简单的测试视频
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('test_video.mp4', fourcc, 10.0, (640, 480))
    
    for i in range(50):  # 5秒视频，10fps
        # 创建彩色背景
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame[:] = (50 + i*2, 100, 150)  # 渐变背景
        
        # 添加文字
        text = f"Test Frame {i+1}/50"
        cv2.putText(frame, text, (200, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 添加一个简单的"人脸"矩形
        cv2.rectangle(frame, (270, 190), (370, 290), (255, 255, 255), 2)
        cv2.putText(frame, "Face", (290, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print("   ✅ 测试视频创建完成: test_video.mp4")
    return 'test_video.mp4'

def run_quick_test():
    """运行快速测试"""
    print("\n🚀 运行视频年龄识别快速测试...")
    
    # 检查模型
    model_path = test_model_loading()
    if not model_path:
        print("   ❌ 无法进行测试，缺少模型文件")
        return
    
    # 检查摄像头
    camera_id = test_camera_access()
    
    if camera_id is not None:
        print(f"\n🎥 启动摄像头测试 (5秒)...")
        print("   请面向摄像头，测试年龄识别功能")
        
        try:
            # 导入视频识别模块
            from video_age_recognition import VideoAgeRecognizer
            
            # 创建识别器
            recognizer = VideoAgeRecognizer(model_path, model_type='simple')
            
            # 打开摄像头
            cap = cv2.VideoCapture(camera_id)
            start_time = time.time()
            
            while time.time() - start_time < 5:  # 测试5秒
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 处理帧
                processed_frame = recognizer.process_frame(frame)
                
                # 显示
                cv2.imshow('Age Recognition Test', processed_frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            cap.release()
            cv2.destroyAllWindows()
            
            print("   ✅ 摄像头年龄识别测试完成")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    else:
        print("   ⚠️ 无摄像头，跳过实时测试")

def main():
    parser = argparse.ArgumentParser(description='视频年龄识别测试工具')
    parser.add_argument('--quick', action='store_true', help='运行快速测试')
    parser.add_argument('--camera', action='store_true', help='测试摄像头')
    parser.add_argument('--model', action='store_true', help='测试模型加载')
    parser.add_argument('--face', action='store_true', help='测试人脸检测')
    parser.add_argument('--env', action='store_true', help='测试环境')
    parser.add_argument('--all', action='store_true', help='运行所有测试')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🧪 视频年龄识别系统测试工具")
    print("=" * 80)
    
    if args.all or (not any([args.quick, args.camera, args.model, args.face, args.env])):
        # 运行所有测试
        test_pytorch_environment()
        test_model_loading()
        test_face_detection()
        
        if args.quick:
            run_quick_test()
    
    else:
        # 运行指定测试
        if args.env:
            test_pytorch_environment()
        
        if args.model:
            test_model_loading()
        
        if args.face:
            test_face_detection()
        
        if args.camera:
            test_camera_access()
        
        if args.quick:
            run_quick_test()
    
    print("\n" + "=" * 80)
    print("🎉 测试完成!")
    print("如果所有测试通过，您可以开始使用视频年龄识别功能")
    print("使用方法: py -3.12 video_age_recognition.py --help")
    print("=" * 80)

if __name__ == "__main__":
    main()
