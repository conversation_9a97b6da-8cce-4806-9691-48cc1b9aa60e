import os
import json
import numpy as np
import shutil
from sklearn.model_selection import train_test_split

def process_megaasian_age_dataset():
    """
    处理MegaAge-Asian数据集，准备用于训练
    """
    print("正在处理MegaAge-Asian数据集...")
    
    # 数据集路径
    megaage_dir = 'megaage_asian'
    train_dir = os.path.join(megaage_dir, 'megaage_asian', 'train')
    test_dir = os.path.join(megaage_dir, 'megaage_asian', 'test')
    list_dir = os.path.join(megaage_dir, 'megaage_asian', 'list')
    
    # 检查数据集目录是否存在
    if not os.path.exists(train_dir):
        print(f"错误: 训练数据目录不存在: {train_dir}")
        return False
        
    if not os.path.exists(list_dir):
        print(f"错误: 标签目录不存在: {list_dir}")
        return False
    
    # 读取训练集标签
    train_names = []
    train_path = os.path.join(list_dir, 'train_name.txt')
    if os.path.exists(train_path):
        with open(train_path, 'r') as f:
            train_names = [line.strip() for line in f.readlines()]
    else:
        print(f"错误: 训练集名称文件不存在: {train_path}")
        return False
    
    train_ages = []
    age_path = os.path.join(list_dir, 'train_age.txt')
    if os.path.exists(age_path):
        with open(age_path, 'r') as f:
            train_ages = [int(line.strip()) for line in f.readlines()]
    else:
        print(f"错误: 训练集年龄文件不存在: {age_path}")
        return False
    
    if len(train_names) != len(train_ages):
        print(f"错误: 训练集名称数量 ({len(train_names)}) 与年龄数量 ({len(train_ages)}) 不匹配")
        return False
    
    print(f"找到 {len(train_names)} 个训练样本")
    
    # 创建训练和验证数据目录
    os.makedirs('data/train', exist_ok=True)
    os.makedirs('data/val', exist_ok=True)
    
    # 将训练数据分为训练集和验证集
    train_data = list(zip(train_names, train_ages))
    train_subset, val_subset = train_test_split(
        train_data, 
        test_size=0.2, 
        random_state=42,
        stratify=[age//10 for _, age in train_data]  # 按年龄段分层
    )
    
    # 创建标注文件
    train_annotations = {}
    val_annotations = {}
    
    # 处理训练集
    print(f"处理训练集图片...")
    for i, (filename, age) in enumerate(train_subset):
        src = os.path.join(train_dir, filename)
        dst = os.path.join('data/train', filename)
        if os.path.exists(src):
            shutil.copy2(src, dst)
            train_annotations[filename] = age
        else:
            print(f"警告: 找不到训练图片: {src}")
        
        if i % 1000 == 0 and i > 0:
            print(f"已处理 {i}/{len(train_subset)} 张训练图片")
    
    # 处理验证集
    print(f"处理验证集图片...")
    for i, (filename, age) in enumerate(val_subset):
        src = os.path.join(train_dir, filename)
        dst = os.path.join('data/val', filename)
        if os.path.exists(src):
            shutil.copy2(src, dst)
            val_annotations[filename] = age
        else:
            print(f"警告: 找不到验证图片: {src}")
            
        if i % 500 == 0 and i > 0:
            print(f"已处理 {i}/{len(val_subset)} 张验证图片")
    
    # 保存标注文件
    with open('data/train_annotations.json', 'w') as f:
        json.dump(train_annotations, f, indent=2)
    
    with open('data/val_annotations.json', 'w') as f:
        json.dump(val_annotations, f, indent=2)
    
    # 生成数据集统计信息
    train_ages_list = list(train_annotations.values())
    val_ages_list = list(val_annotations.values())
    
    if not train_ages_list or not val_ages_list:
        print("错误: 处理后的数据集为空")
        return False
    
    print(f"\n数据集统计:")
    print(f"训练集: {len(train_annotations)} 张图片")
    print(f"  - 年龄范围: {min(train_ages_list)}-{max(train_ages_list)}岁")
    print(f"  - 平均年龄: {sum(train_ages_list)/len(train_ages_list):.1f}岁")
    
    print(f"验证集: {len(val_annotations)} 张图片")
    print(f"  - 年龄范围: {min(val_ages_list)}-{max(val_ages_list)}岁")
    print(f"  - 平均年龄: {sum(val_ages_list)/len(val_ages_list):.1f}岁")
    
    return True

if __name__ == '__main__':
    if process_megaasian_age_dataset():
        print("\n数据集处理完成! 现在可以运行训练脚本:")
        print("python train_age_model.py")
    else:
        print("\n数据集处理失败，请检查数据集路径和文件")