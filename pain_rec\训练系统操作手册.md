# 年龄识别训练系统操作手册

## 📋 目录
1. [系统概述](#系统概述)
2. [环境要求](#环境要求)
3. [快速开始](#快速开始)
4. [命令行参数](#命令行参数)
5. [断点续训功能](#断点续训功能)
6. [GPU监控功能](#GPU监控功能)
7. [文件结构](#文件结构)
8. [常见问题](#常见问题)
9. [高级用法](#高级用法)

## 🚀 系统概述

本训练系统是一个功能完整的年龄识别深度学习训练平台，具有以下特性：

### ✨ 核心功能
- **断点续训**: 支持训练中断后从检查点继续
- **GPU监控**: 实时显示GPU利用率、内存使用、温度等
- **混合精度训练**: 自动启用AMP加速训练并节省内存
- **智能检查点**: 自动保存最佳模型和定期检查点
- **详细日志**: 完整的训练历史记录和性能统计
- **多模型支持**: 简单模型和高级模型可选

### 🎯 适用场景
- 长时间训练任务
- 需要中断和恢复的训练
- GPU资源监控和优化
- 模型性能对比实验

## 🔧 环境要求

### 必需依赖
```bash
# Python 3.9+ (推荐 3.12)
py -3.12 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
py -3.12 -m pip install pillow matplotlib numpy scikit-learn psutil
```

### 硬件要求
- **GPU**: NVIDIA GPU (推荐RTX 4070或更高)
- **内存**: 16GB+ 系统内存
- **存储**: 10GB+ 可用空间 (数据集 + 检查点)

### 数据集要求
- 训练数据: `data/train/` 目录下的图片文件
- 验证数据: `data/val/` 目录下的图片文件
- 标注文件: `data/train_annotations.json` 和 `data/val_annotations.json`

## 🚀 快速开始

### 1. 首次训练
```bash
# 基本训练 (使用默认参数)
py -3.12 train_age_checkpoint.py

# 指定参数训练
py -3.12 train_age_checkpoint.py --epochs 100 --batch-size 256 --lr 0.001
```

### 2. 断点续训
```bash
# 从最新检查点继续
py -3.12 train_age_checkpoint.py --resume

# 从指定检查点继续
py -3.12 train_age_checkpoint.py --checkpoint checkpoints/checkpoint_epoch_20.pth
```

### 3. 查看检查点
```bash
# 列出所有可用检查点
py -3.12 train_age_checkpoint.py --list-checkpoints
```

### 4. 导出训练日志
```bash
# 导出详细训练日志
py -3.12 train_age_checkpoint.py --export-log
```

## 📝 命令行参数

### 基本参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--epochs` | int | 50 | 训练轮次 |
| `--batch-size` | int | 128 | 批次大小 |
| `--lr` | float | 0.001 | 学习率 |
| `--model-type` | str | simple | 模型类型 (simple/advanced) |

### 断点续训参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--resume` | flag | False | 从最新检查点继续训练 |
| `--checkpoint` | str | None | 指定检查点文件路径 |
| `--checkpoint-dir` | str | checkpoints | 检查点目录 |
| `--save-every` | int | 5 | 每N轮次保存检查点 |

### 其他参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--no-amp` | flag | False | 禁用混合精度训练 |
| `--list-checkpoints` | flag | False | 列出所有检查点 |
| `--export-log` | flag | False | 导出训练日志 |

## 💾 断点续训功能

### 自动保存机制
系统会在以下情况自动保存检查点：
1. **定期保存**: 每N轮次 (默认5轮次)
2. **最佳模型**: 验证MAE创新纪录时
3. **训练结束**: 完成所有轮次时
4. **用户中断**: Ctrl+C中断时

### 检查点内容
每个检查点包含：
- 模型权重 (`model_state_dict`)
- 优化器状态 (`optimizer_state_dict`)
- 学习率调度器状态 (`scheduler_state_dict`)
- 训练历史 (`train_losses`, `val_losses`, `val_maes`)
- 混合精度缩放器状态 (`scaler_state_dict`)
- 时间戳和元数据

### 使用示例

#### 场景1: 训练被意外中断
```bash
# 原始训练命令
py -3.12 train_age_checkpoint.py --epochs 100 --batch-size 256

# 训练在第30轮次被中断，重新启动继续训练
py -3.12 train_age_checkpoint.py --resume --epochs 100 --batch-size 256
```

#### 场景2: 从特定检查点开始
```bash
# 查看可用检查点
py -3.12 train_age_checkpoint.py --list-checkpoints

# 输出示例:
# 📋 可用检查点 (4 个):
#    best_model.pth: 轮次 25, MAE 7.234岁, 2024-01-15T14:30:00
#    checkpoint_epoch_20.pth: 轮次 20, MAE 8.123岁, 2024-01-15T13:45:00
#    checkpoint_epoch_25.pth: 轮次 25, MAE 7.234岁, 2024-01-15T14:30:00
#    latest_checkpoint.pth: 轮次 30, MAE 7.456岁, 2024-01-15T15:15:00

# 从第20轮次的检查点继续
py -3.12 train_age_checkpoint.py --checkpoint checkpoints/checkpoint_epoch_20.pth
```

#### 场景3: 实验不同参数
```bash
# 从最佳模型开始，使用更小的学习率精调
py -3.12 train_age_checkpoint.py --checkpoint checkpoints/best_model.pth --lr 0.0001 --epochs 150
```

## 🎮 GPU监控功能

### 实时监控信息
训练过程中会显示：
- **GPU利用率**: 实时GPU使用百分比
- **GPU内存**: 已使用/总内存 (GB) 和使用率
- **GPU温度**: 实时温度监控
- **PyTorch内存**: 已分配和缓存的内存

### 监控输出示例
```
📊 批次  20/500 ( 4.0%) | 损失: 156.7234 | 速度: 245.3 样本/秒 | ETA:  180秒
   🎮 GPU利用率:  95.2% | GPU内存: 8.4GB/12.9GB (65.1%) | 温度:  72°C
   🔧 PyTorch: 6.23GB 已分配 | 8.45GB 缓存
```

### GPU优化建议
根据监控信息优化训练：

1. **GPU利用率低 (<80%)**:
   - 增加批次大小
   - 检查数据加载瓶颈
   - 启用混合精度训练

2. **GPU内存不足**:
   - 减少批次大小
   - 启用混合精度训练
   - 使用梯度累积

3. **GPU温度过高 (>85°C)**:
   - 检查散热系统
   - 降低批次大小
   - 调整风扇曲线

## 📁 文件结构

```
pain_rec/
├── train_age_checkpoint.py          # 主训练脚本
├── 训练系统操作手册.md              # 本手册
├── data/                            # 数据目录
│   ├── train/                       # 训练图片
│   ├── val/                         # 验证图片
│   ├── train_annotations.json       # 训练标注
│   └── val_annotations.json         # 验证标注
├── checkpoints/                     # 检查点目录
│   ├── latest_checkpoint.pth        # 最新检查点
│   ├── best_model.pth              # 最佳模型
│   ├── checkpoint_epoch_*.pth       # 轮次检查点
│   └── training_log.json           # 训练日志
└── logs/                           # 日志目录 (可选)
```

## ❓ 常见问题

### Q1: 如何选择合适的批次大小？
**A**: 根据GPU内存选择：
- RTX 4070 (12GB): 128-256
- RTX 3080 (10GB): 96-192  
- RTX 3060 (8GB): 64-128

运行时观察GPU内存使用率，目标是80-90%利用率。

### Q2: 训练中断后如何恢复？
**A**: 使用 `--resume` 参数：
```bash
py -3.12 train_age_checkpoint.py --resume
```

### Q3: 如何查看训练历史？
**A**: 导出训练日志：
```bash
py -3.12 train_age_checkpoint.py --export-log
```
然后查看 `checkpoints/training_log.json`

### Q4: GPU利用率低怎么办？
**A**: 尝试以下方法：
1. 增加批次大小
2. 启用混合精度 (默认启用)
3. 检查数据加载速度
4. 使用更复杂的模型

### Q5: 内存不足错误？
**A**: 解决方案：
1. 减少批次大小
2. 启用混合精度训练
3. 设置 `--no-amp` 如果AMP有问题
4. 检查系统内存使用

### Q6: 如何比较不同模型？
**A**: 使用不同的检查点目录：
```bash
# 简单模型
py -3.12 train_age_checkpoint.py --model-type simple --checkpoint-dir checkpoints_simple

# 高级模型  
py -3.12 train_age_checkpoint.py --model-type advanced --checkpoint-dir checkpoints_advanced
```

## 🔬 高级用法

### 1. 自定义训练策略
```bash
# 长时间训练，频繁保存
py -3.12 train_age_checkpoint.py --epochs 200 --save-every 2 --lr 0.0005

# 快速实验，大批次
py -3.12 train_age_checkpoint.py --epochs 20 --batch-size 512 --lr 0.002
```

### 2. 多阶段训练
```bash
# 阶段1: 快速训练
py -3.12 train_age_checkpoint.py --epochs 50 --lr 0.001 --checkpoint-dir stage1

# 阶段2: 精调
py -3.12 train_age_checkpoint.py --checkpoint stage1/best_model.pth --epochs 100 --lr 0.0001 --checkpoint-dir stage2
```

### 3. 模型对比实验
```bash
# 实验1: 简单模型
py -3.12 train_age_checkpoint.py --model-type simple --checkpoint-dir exp_simple

# 实验2: 高级模型
py -3.12 train_age_checkpoint.py --model-type advanced --checkpoint-dir exp_advanced

# 实验3: 不同学习率
py -3.12 train_age_checkpoint.py --lr 0.01 --checkpoint-dir exp_lr001
```

### 4. 性能调优
```bash
# 最大化GPU利用率
py -3.12 train_age_checkpoint.py --batch-size 256 --model-type advanced

# 内存受限环境
py -3.12 train_age_checkpoint.py --batch-size 64 --no-amp

# 快速验证
py -3.12 train_age_checkpoint.py --epochs 5 --save-every 1
```

## 📊 性能基准

### 硬件性能参考
| GPU型号 | 推荐批次大小 | 预期速度 (样本/秒) | 内存使用 |
|---------|-------------|-------------------|----------|
| RTX 4070 SUPER | 256 | 300-400 | 8-10GB |
| RTX 4070 | 192 | 250-350 | 6-8GB |
| RTX 3080 | 128 | 200-300 | 6-8GB |
| RTX 3060 | 96 | 150-250 | 4-6GB |

### 模型性能对比
| 模型类型 | 参数量 | 训练速度 | 预期MAE |
|----------|--------|----------|---------|
| Simple | 1.7M | 快 | 7-9岁 |
| Advanced | 23.5M | 中等 | 5-7岁 |

---

## 📞 技术支持

如有问题，请检查：
1. 环境依赖是否正确安装
2. 数据集是否准备完整
3. GPU驱动是否最新
4. 检查点目录权限是否正确

**祝您训练顺利！** 🎉
