#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频年龄识别脚本
功能：
1. 检测视频中的人脸
2. 调用训练好的年龄识别模型预测年龄
3. 在视频上实时显示年龄信息
4. 支持摄像头实时识别和视频文件处理
"""

import cv2
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import argparse
import os
import time
from datetime import datetime

# 导入训练脚本中的模型定义
from train_age_checkpoint import AgeRecognitionCNN

class VideoAgeRecognizer:
    """视频年龄识别器"""
    
    def __init__(self, model_path, model_type='simple', device=None):
        """
        初始化年龄识别器
        
        Args:
            model_path: 训练好的模型路径
            model_type: 模型类型 ('simple' 或 'advanced')
            device: 计算设备 ('cuda' 或 'cpu')
        """
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = AgeRecognitionCNN(model_type=model_type)
        self.load_model(model_path)
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 人脸检测器
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # 统计信息
        self.frame_count = 0
        self.detection_count = 0
        self.processing_times = []
        
        print(f"✅ 年龄识别器初始化完成")
        print(f"   模型类型: {model_type}")
        print(f"   计算设备: {self.device}")
        print(f"   模型路径: {model_path}")
    
    def load_model(self, model_path):
        """加载训练好的模型"""
        try:
            if model_path.endswith('.pth'):
                # 加载检查点文件
                checkpoint = torch.load(model_path, map_location=self.device)
                if 'model_state_dict' in checkpoint:
                    self.model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"   从检查点加载: 轮次 {checkpoint.get('epoch', '未知')}, MAE {checkpoint.get('val_mae', '未知'):.3f}岁")
                else:
                    self.model.load_state_dict(checkpoint)
            else:
                raise ValueError("不支持的模型文件格式")
            
            self.model.to(self.device)
            self.model.eval()
            
        except Exception as e:
            raise RuntimeError(f"模型加载失败: {e}")
    
    def detect_faces(self, frame):
        """检测人脸"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30),
            flags=cv2.CASCADE_SCALE_IMAGE
        )
        return faces
    
    def predict_age(self, face_image):
        """预测年龄"""
        try:
            # 转换为PIL图像
            face_pil = Image.fromarray(cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB))
            
            # 预处理
            input_tensor = self.transform(face_pil).unsqueeze(0).to(self.device)
            
            # 预测
            with torch.no_grad():
                age = self.model(input_tensor).item()
            
            return max(0, min(100, age))  # 限制年龄范围在0-100
            
        except Exception as e:
            print(f"年龄预测错误: {e}")
            return None
    
    def process_frame(self, frame):
        """处理单帧图像"""
        start_time = time.time()
        
        # 检测人脸
        faces = self.detect_faces(frame)
        
        # 处理每个检测到的人脸
        for i, (x, y, w, h) in enumerate(faces):
            # 提取人脸区域
            face_roi = frame[y:y+h, x:x+w]
            
            # 预测年龄
            age = self.predict_age(face_roi)
            
            if age is not None:
                # 绘制人脸框
                cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                
                # 显示年龄信息
                age_text = f"Age: {age:.1f}"
                text_size = cv2.getTextSize(age_text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                
                # 背景框
                cv2.rectangle(frame, (x, y-30), (x+text_size[0]+10, y), (0, 255, 0), -1)
                
                # 年龄文字
                cv2.putText(frame, age_text, (x+5, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                
                # 人脸编号
                face_id = f"#{i+1}"
                cv2.putText(frame, face_id, (x+5, y+h-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                self.detection_count += 1
        
        # 记录处理时间
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        # 显示统计信息
        self.draw_stats(frame, len(faces), processing_time)
        
        self.frame_count += 1
        return frame
    
    def draw_stats(self, frame, face_count, processing_time):
        """绘制统计信息"""
        h, w = frame.shape[:2]
        
        # 统计信息
        fps = 1.0 / processing_time if processing_time > 0 else 0
        avg_time = np.mean(self.processing_times[-30:]) if self.processing_times else 0
        avg_fps = 1.0 / avg_time if avg_time > 0 else 0
        
        stats = [
            f"Frame: {self.frame_count}",
            f"Faces: {face_count}",
            f"Total Detections: {self.detection_count}",
            f"FPS: {fps:.1f}",
            f"Avg FPS: {avg_fps:.1f}",
            f"Device: {self.device.upper()}"
        ]
        
        # 绘制半透明背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (300, 30 + len(stats) * 25), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # 绘制统计文字
        for i, stat in enumerate(stats):
            cv2.putText(frame, stat, (15, 30 + i * 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    def process_video(self, video_path, output_path=None, show_preview=True):
        """处理视频文件"""
        print(f"\n🎬 开始处理视频: {video_path}")
        
        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"   分辨率: {width}x{height}")
        print(f"   帧率: {fps} FPS")
        print(f"   总帧数: {total_frames}")
        
        # 设置输出视频
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            print(f"   输出路径: {output_path}")
        
        start_time = time.time()
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 处理帧
                processed_frame = self.process_frame(frame)
                
                # 保存到输出视频
                if out:
                    out.write(processed_frame)
                
                # 显示预览
                if show_preview:
                    cv2.imshow('Age Recognition', processed_frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        print("\n用户中断处理")
                        break
                
                # 显示进度
                if self.frame_count % 30 == 0:
                    progress = self.frame_count / total_frames * 100
                    elapsed = time.time() - start_time
                    eta = elapsed / self.frame_count * (total_frames - self.frame_count)
                    print(f"   进度: {progress:.1f}% ({self.frame_count}/{total_frames}), "
                          f"ETA: {eta:.1f}秒")
        
        finally:
            cap.release()
            if out:
                out.release()
            cv2.destroyAllWindows()
        
        # 处理完成统计
        total_time = time.time() - start_time
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print(f"\n✅ 视频处理完成!")
        print(f"   处理帧数: {self.frame_count}")
        print(f"   检测人脸: {self.detection_count}")
        print(f"   总耗时: {total_time:.1f}秒")
        print(f"   平均FPS: {avg_fps:.1f}")
    
    def process_camera(self, camera_id=0):
        """处理摄像头实时视频"""
        print(f"\n📹 开始摄像头实时识别 (摄像头ID: {camera_id})")
        print("按 'q' 退出, 按 's' 截图")
        
        # 打开摄像头
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            raise ValueError(f"无法打开摄像头: {camera_id}")
        
        # 设置摄像头参数
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        start_time = time.time()
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("无法读取摄像头画面")
                    break
                
                # 处理帧
                processed_frame = self.process_frame(frame)
                
                # 显示画面
                cv2.imshow('Real-time Age Recognition', processed_frame)
                
                # 按键处理
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    # 截图
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    screenshot_path = f"screenshot_{timestamp}.jpg"
                    cv2.imwrite(screenshot_path, processed_frame)
                    print(f"截图保存: {screenshot_path}")
        
        finally:
            cap.release()
            cv2.destroyAllWindows()
        
        # 统计信息
        total_time = time.time() - start_time
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print(f"\n✅ 摄像头识别结束!")
        print(f"   运行时间: {total_time:.1f}秒")
        print(f"   处理帧数: {self.frame_count}")
        print(f"   检测人脸: {self.detection_count}")
        print(f"   平均FPS: {avg_fps:.1f}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='视频年龄识别系统')
    
    # 必需参数
    parser.add_argument('--model', type=str, required=True, 
                       help='训练好的模型路径 (如: checkpoints/best_model.pth)')
    
    # 输入源
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--video', type=str, help='输入视频文件路径')
    group.add_argument('--camera', type=int, help='摄像头ID (默认: 0)')
    
    # 可选参数
    parser.add_argument('--output', type=str, help='输出视频路径 (仅视频模式)')
    parser.add_argument('--model-type', choices=['simple', 'advanced'], default='simple',
                       help='模型类型 (默认: simple)')
    parser.add_argument('--device', choices=['cuda', 'cpu'], 
                       help='计算设备 (默认: 自动选择)')
    parser.add_argument('--no-preview', action='store_true', 
                       help='不显示预览窗口 (仅视频模式)')
    
    return parser.parse_args()

def main():
    args = parse_args()
    
    print("=" * 80)
    print("🎬 视频年龄识别系统")
    print("=" * 80)
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        print("请确保已完成模型训练并指定正确的模型路径")
        return
    
    # 检查输入文件
    if args.video and not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return
    
    try:
        # 创建年龄识别器
        recognizer = VideoAgeRecognizer(
            model_path=args.model,
            model_type=args.model_type,
            device=args.device
        )
        
        # 处理视频或摄像头
        if args.video:
            recognizer.process_video(
                video_path=args.video,
                output_path=args.output,
                show_preview=not args.no_preview
            )
        else:
            recognizer.process_camera(camera_id=args.camera)
    
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        return
    
    print("\n🎉 年龄识别完成!")

if __name__ == "__main__":
    main()
