#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化视频年龄识别脚本
调用 best_optimized_age_model.pth 模型识别视频中人物年龄
"""

import cv2
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import argparse
import os
import time

class OptimizedCNN(nn.Module):
    """优化的CNN模型 - 与训练时的模型结构一致"""
    def __init__(self):
        super(OptimizedCNN, self).__init__()
        
        self.features = nn.Sequential(
            # 第一组卷积块 (224->112)
            nn.Conv2d(3, 64, 7, 2, 3),
            nn.BatchNorm2d(64),
            nn.ReL<PERSON>(inplace=True),
            nn.MaxPool2d(3, 2, 1),
            
            # 第二组卷积块 (56->28)
            nn.Conv2d(64, 128, 3, 1, 1),
            nn.<PERSON>chNorm2d(128),
            nn.<PERSON><PERSON><PERSON>(inplace=True),
            nn.Conv2d(128, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.<PERSON><PERSON><PERSON>(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第三组卷积块 (28->14)
            nn.Conv2d(128, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第四组卷积块 (14->7)
            nn.Conv2d(256, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第五组卷积块 (7->1)
            nn.Conv2d(512, 1024, 3, 1, 1),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 1024, 3, 1, 1),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(1024, 2048),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(2048, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 1)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x.squeeze()

class VideoAgeDetector:
    """视频年龄检测器"""
    
    def __init__(self, model_path='best_optimized_age_model.pth'):
        """初始化检测器"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        print(f"🧠 加载模型: {model_path}")
        self.model = OptimizedCNN()
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.to(self.device)
        self.model.eval()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 人脸检测器
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        print(f"✅ 年龄检测器初始化完成")
        print(f"   计算设备: {self.device}")
        print(f"   人脸检测器: {'✅' if not self.face_cascade.empty() else '❌'}")
    
    def detect_faces(self, frame):
        """检测人脸"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(50, 50)  # 最小人脸尺寸
        )
        return faces
    
    def predict_age(self, face_image):
        """预测年龄"""
        try:
            # 转换为PIL图像
            face_pil = Image.fromarray(cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB))
            
            # 预处理
            input_tensor = self.transform(face_pil).unsqueeze(0).to(self.device)
            
            # 预测
            with torch.no_grad():
                age = self.model(input_tensor).item()
            
            return max(0, min(100, age))  # 限制年龄范围
            
        except Exception as e:
            print(f"年龄预测错误: {e}")
            return None
    
    def process_frame(self, frame):
        """处理单帧"""
        # 检测人脸
        faces = self.detect_faces(frame)
        
        # 处理每个人脸
        for i, (x, y, w, h) in enumerate(faces):
            # 提取人脸区域
            face_roi = frame[y:y+h, x:x+w]
            
            # 预测年龄
            age = self.predict_age(face_roi)
            
            if age is not None:
                # 绘制人脸框
                cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                
                # 显示年龄
                age_text = f"Age: {age:.1f}"
                
                # 背景框
                text_size = cv2.getTextSize(age_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                cv2.rectangle(frame, (x, y-35), (x+text_size[0]+10, y-5), (0, 255, 0), -1)
                
                # 年龄文字
                cv2.putText(frame, age_text, (x+5, y-15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        return frame, len(faces)
    
    def process_video(self, video_path, output_path=None):
        """处理视频文件"""
        print(f"\n🎬 处理视频: {video_path}")
        
        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ 无法打开视频: {video_path}")
            return
        
        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"   分辨率: {width}x{height}")
        print(f"   帧率: {fps} FPS")
        print(f"   总帧数: {total_frames}")
        
        # 设置输出
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            print(f"   输出: {output_path}")
        
        frame_count = 0
        face_count = 0
        start_time = time.time()
        
        print(f"\n🚀 开始处理... (按 'q' 退出预览)")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 处理帧
            processed_frame, faces_in_frame = self.process_frame(frame)
            face_count += faces_in_frame
            frame_count += 1
            
            # 添加统计信息
            info_text = f"Frame: {frame_count}/{total_frames} | Faces: {faces_in_frame} | Total: {face_count}"
            cv2.putText(processed_frame, info_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 保存到输出视频
            if out:
                out.write(processed_frame)
            
            # 显示预览
            cv2.imshow('Age Detection', processed_frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                print("\n用户中断处理")
                break
            
            # 显示进度
            if frame_count % 30 == 0:
                progress = frame_count / total_frames * 100
                elapsed = time.time() - start_time
                fps_current = frame_count / elapsed
                print(f"   进度: {progress:.1f}% | 速度: {fps_current:.1f} FPS | 检测人脸: {face_count}")
        
        # 清理
        cap.release()
        if out:
            out.release()
        cv2.destroyAllWindows()
        
        # 统计
        total_time = time.time() - start_time
        print(f"\n✅ 处理完成!")
        print(f"   处理帧数: {frame_count}")
        print(f"   检测人脸: {face_count}")
        print(f"   总耗时: {total_time:.1f}秒")
        print(f"   平均FPS: {frame_count/total_time:.1f}")
    
    def process_camera(self, camera_id=0):
        """处理摄像头"""
        print(f"\n📹 启动摄像头 {camera_id}")
        print("按 'q' 退出, 按 's' 截图")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"❌ 无法打开摄像头 {camera_id}")
            return
        
        frame_count = 0
        face_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 处理帧
            processed_frame, faces_in_frame = self.process_frame(frame)
            face_count += faces_in_frame
            frame_count += 1
            
            # 添加统计信息
            elapsed = time.time() - start_time
            fps = frame_count / elapsed if elapsed > 0 else 0
            info_text = f"FPS: {fps:.1f} | Faces: {faces_in_frame} | Total: {face_count}"
            cv2.putText(processed_frame, info_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 显示
            cv2.imshow('Real-time Age Detection', processed_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 截图
                screenshot_name = f"age_detection_{int(time.time())}.jpg"
                cv2.imwrite(screenshot_name, processed_frame)
                print(f"截图保存: {screenshot_name}")
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n✅ 摄像头识别结束!")
        print(f"   运行时间: {elapsed:.1f}秒")
        print(f"   处理帧数: {frame_count}")
        print(f"   检测人脸: {face_count}")

def main():
    parser = argparse.ArgumentParser(description='视频年龄识别 - 调用best_optimized_age_model.pth')
    
    # 输入源
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--video', type=str, help='输入视频文件')
    group.add_argument('--camera', type=int, help='摄像头ID (默认: 0)')
    
    # 可选参数
    parser.add_argument('--output', type=str, help='输出视频路径')
    parser.add_argument('--model', type=str, default='best_optimized_age_model.pth',
                       help='模型文件路径')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎬 视频年龄识别系统")
    print("=" * 60)
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    # 检查输入文件
    if args.video and not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return
    
    try:
        # 创建检测器
        detector = VideoAgeDetector(args.model)
        
        # 处理视频或摄像头
        if args.video:
            detector.process_video(args.video, args.output)
        else:
            detector.process_camera(args.camera)
    
    except Exception as e:
        print(f"❌ 处理错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
