#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支持断点续训的年龄识别训练脚本
功能：
1. 自动保存训练检查点
2. 支持从断点继续训练
3. 详细的GPU监控
4. 训练历史记录
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import torchvision.transforms as transforms
import os
import json
import time
import numpy as np
import subprocess
import sys
import argparse
import pickle
from datetime import datetime

# 启用混合精度训练
from torch.cuda.amp import autocast, GradScaler

def get_gpu_info():
    """获取GPU信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                parts = line.split(', ')
                if len(parts) >= 4:
                    return {
                        'gpu_utilization': float(parts[0]),
                        'memory_used_mb': float(parts[1]),
                        'memory_total_mb': float(parts[2]),
                        'temperature': float(parts[3])
                    }
    except Exception:
        pass
    
    # 备用方案：使用PyTorch信息
    if torch.cuda.is_available():
        return {
            'gpu_utilization': 0,  # 无法获取
            'memory_used_mb': torch.cuda.memory_allocated() / 1e6,
            'memory_total_mb': torch.cuda.get_device_properties(0).total_memory / 1e6,
            'temperature': 0  # 无法获取
        }
    return None

class AgeDataset(Dataset):
    def __init__(self, data_dir, annotations_file, transform=None):
        self.data_dir = data_dir
        self.transform = transform
        
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        self.image_names = list(self.annotations.keys())
        print(f"✅ 加载了 {len(self.image_names)} 张图片")
    
    def __len__(self):
        return len(self.image_names)
    
    def __getitem__(self, idx):
        image_name = self.image_names[idx]
        image_path = os.path.join(self.data_dir, image_name)
        age = self.annotations[image_name]
        
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image, age

class AgeRecognitionCNN(nn.Module):
    """年龄识别CNN模型"""
    def __init__(self, model_type='simple'):
        super(AgeRecognitionCNN, self).__init__()
        self.model_type = model_type
        
        if model_type == 'simple':
            self._build_simple_model()
        elif model_type == 'advanced':
            self._build_advanced_model()
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
    
    def _build_simple_model(self):
        """构建简单模型"""
        self.features = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
            
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            nn.Conv2d(128, 256, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            nn.Conv2d(256, 512, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 1)
        )
    
    def _build_advanced_model(self):
        """构建高级模型"""
        self.features = nn.Sequential(
            # 第一组卷积块
            nn.Conv2d(3, 64, 7, 2, 3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(3, 2, 1),
            
            # 第二组卷积块
            nn.Conv2d(64, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第三组卷积块
            nn.Conv2d(128, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第四组卷积块
            nn.Conv2d(256, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第五组卷积块
            nn.Conv2d(512, 1024, 3, 1, 1),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(1024, 2048),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(2048, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 1)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x.squeeze()

class CheckpointTrainer:
    """支持断点续训的训练器"""
    
    def __init__(self, model, learning_rate=0.001, use_amp=True, checkpoint_dir='checkpoints'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = model.to(self.device)
        self.use_amp = use_amp and torch.cuda.is_available()
        self.checkpoint_dir = checkpoint_dir
        
        # 创建检查点目录
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        self.optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', patience=5, factor=0.5)
        self.criterion = nn.MSELoss()
        self.scaler = GradScaler() if self.use_amp else None
        
        # 训练状态
        self.start_epoch = 0
        self.best_mae = float('inf')
        self.train_losses = []
        self.val_losses = []
        self.val_maes = []
        self.training_history = []
        
        print(f"🚀 断点续训训练器初始化完成")
        print(f"   设备: {self.device}")
        print(f"   混合精度: {'✅' if self.use_amp else '❌'}")
        print(f"   检查点目录: {checkpoint_dir}")
        
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            print(f"   GPU: {gpu_props.name}")
            print(f"   GPU内存: {gpu_props.total_memory/1e9:.1f}GB")
    
    def save_checkpoint(self, epoch, val_mae, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_mae': self.best_mae,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'val_maes': self.val_maes,
            'training_history': self.training_history,
            'scaler_state_dict': self.scaler.state_dict() if self.scaler else None,
            'timestamp': datetime.now().isoformat(),
            'val_mae': val_mae
        }
        
        # 保存最新检查点
        latest_path = os.path.join(self.checkpoint_dir, 'latest_checkpoint.pth')
        torch.save(checkpoint, latest_path)
        
        # 保存轮次检查点
        epoch_path = os.path.join(self.checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
        torch.save(checkpoint, epoch_path)
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            print(f"💾 保存最佳模型: {best_path}")
        
        print(f"💾 保存检查点: 轮次 {epoch}, MAE {val_mae:.3f}岁")
    
    def load_checkpoint(self, checkpoint_path=None):
        """加载检查点"""
        if checkpoint_path is None:
            checkpoint_path = os.path.join(self.checkpoint_dir, 'latest_checkpoint.pth')
        
        if not os.path.exists(checkpoint_path):
            print(f"⚠️ 检查点文件不存在: {checkpoint_path}")
            return False
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
            self.start_epoch = checkpoint['epoch'] + 1
            self.best_mae = checkpoint['best_mae']
            self.train_losses = checkpoint['train_losses']
            self.val_losses = checkpoint['val_losses']
            self.val_maes = checkpoint['val_maes']
            self.training_history = checkpoint.get('training_history', [])
            
            if self.scaler and checkpoint.get('scaler_state_dict'):
                self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
            
            print(f"✅ 成功加载检查点: {checkpoint_path}")
            print(f"   继续从轮次 {self.start_epoch} 开始")
            print(f"   当前最佳MAE: {self.best_mae:.3f}岁")
            print(f"   检查点时间: {checkpoint.get('timestamp', '未知')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载检查点失败: {e}")
            return False
    
    def list_checkpoints(self):
        """列出所有可用的检查点"""
        if not os.path.exists(self.checkpoint_dir):
            print("📁 检查点目录不存在")
            return []
        
        checkpoints = []
        for file in os.listdir(self.checkpoint_dir):
            if file.endswith('.pth'):
                file_path = os.path.join(self.checkpoint_dir, file)
                try:
                    checkpoint = torch.load(file_path, map_location='cpu')
                    checkpoints.append({
                        'file': file,
                        'path': file_path,
                        'epoch': checkpoint.get('epoch', 0),
                        'mae': checkpoint.get('val_mae', float('inf')),
                        'timestamp': checkpoint.get('timestamp', '未知')
                    })
                except:
                    continue
        
        # 按轮次排序
        checkpoints.sort(key=lambda x: x['epoch'])
        
        print(f"📋 可用检查点 ({len(checkpoints)} 个):")
        for cp in checkpoints:
            print(f"   {cp['file']}: 轮次 {cp['epoch']}, MAE {cp['mae']:.3f}岁, {cp['timestamp']}")
        
        return checkpoints

    def train_epoch(self, train_loader, epoch_num):
        """训练一个轮次"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        samples_processed = 0
        epoch_start_time = time.time()

        print(f"\n🏃 开始第 {epoch_num} 轮次训练 (共 {len(train_loader)} 批次)")

        for batch_idx, (images, ages) in enumerate(train_loader):
            batch_start_time = time.time()

            images = images.to(self.device, non_blocking=True)
            ages = ages.float().to(self.device, non_blocking=True)

            self.optimizer.zero_grad()

            if self.use_amp:
                with autocast():
                    outputs = self.model(images)
                    loss = self.criterion(outputs, ages)

                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(images)
                loss = self.criterion(outputs, ages)
                loss.backward()
                self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1
            samples_processed += images.size(0)

            batch_time = time.time() - batch_start_time
            samples_per_sec = images.size(0) / batch_time

            # 每20个批次显示详细信息
            if batch_idx % 20 == 0:
                progress = (batch_idx + 1) / len(train_loader) * 100
                elapsed = time.time() - epoch_start_time
                eta = elapsed / (batch_idx + 1) * (len(train_loader) - batch_idx - 1)

                # 获取GPU信息
                gpu_info = get_gpu_info()

                print(f"  📊 批次 {batch_idx:3d}/{len(train_loader)} ({progress:5.1f}%) | "
                      f"损失: {loss.item():7.4f} | "
                      f"速度: {samples_per_sec:5.1f} 样本/秒 | "
                      f"ETA: {eta:5.1f}秒")

                if gpu_info:
                    print(f"     🎮 GPU利用率: {gpu_info['gpu_utilization']:5.1f}% | "
                          f"GPU内存: {gpu_info['memory_used_mb']/1024:.1f}GB/"
                          f"{gpu_info['memory_total_mb']/1024:.1f}GB "
                          f"({gpu_info['memory_used_mb']/gpu_info['memory_total_mb']*100:5.1f}%) | "
                          f"温度: {gpu_info['temperature']:3.0f}°C")

                # PyTorch内存信息
                if torch.cuda.is_available():
                    allocated = torch.cuda.memory_allocated() / 1e9
                    reserved = torch.cuda.memory_reserved() / 1e9
                    print(f"     🔧 PyTorch: {allocated:.2f}GB 已分配 | {reserved:.2f}GB 缓存")

        epoch_time = time.time() - epoch_start_time
        avg_samples_per_sec = samples_processed / epoch_time
        avg_loss = total_loss / num_batches

        print(f"\n✅ 轮次 {epoch_num} 训练完成:")
        print(f"   ⏱️  耗时: {epoch_time:.1f}秒")
        print(f"   🚀 平均速度: {avg_samples_per_sec:.1f} 样本/秒")
        print(f"   📈 处理样本: {samples_processed:,}")
        print(f"   📉 平均损失: {avg_loss:.6f}")

        return avg_loss

    def validate(self, val_loader, epoch_num):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_samples = 0

        print(f"\n🔍 开始第 {epoch_num} 轮次验证...")

        with torch.no_grad():
            for batch_idx, (images, ages) in enumerate(val_loader):
                images = images.to(self.device, non_blocking=True)
                ages = ages.float().to(self.device, non_blocking=True)

                if self.use_amp:
                    with autocast():
                        outputs = self.model(images)
                        loss = self.criterion(outputs, ages)
                else:
                    outputs = self.model(images)
                    loss = self.criterion(outputs, ages)

                mae = torch.mean(torch.abs(outputs - ages))

                total_loss += loss.item() * images.size(0)
                total_mae += mae.item() * images.size(0)
                num_samples += images.size(0)

        avg_loss = total_loss / num_samples
        avg_mae = total_mae / num_samples

        print(f"✅ 验证完成: 损失 {avg_loss:.6f} | MAE {avg_mae:.3f}岁")

        return avg_loss, avg_mae

    def train(self, train_loader, val_loader, epochs=50, save_every=5):
        """主训练循环"""
        print(f"\n🚀 开始训练 {epochs} 轮次 (从轮次 {self.start_epoch} 开始)")
        print(f"💾 每 {save_every} 轮次保存一次检查点")

        training_start_time = time.time()

        try:
            for epoch in range(self.start_epoch, epochs):
                epoch_start_time = time.time()

                print(f"\n{'='*80}")
                print(f"🔄 轮次 {epoch+1}/{epochs}")
                print(f"{'='*80}")

                # 训练
                train_loss = self.train_epoch(train_loader, epoch+1)

                # 验证
                val_loss, val_mae = self.validate(val_loader, epoch+1)

                # 学习率调度
                old_lr = self.optimizer.param_groups[0]['lr']
                self.scheduler.step(val_loss)
                new_lr = self.optimizer.param_groups[0]['lr']

                # 记录历史
                self.train_losses.append(train_loss)
                self.val_losses.append(val_loss)
                self.val_maes.append(val_mae)

                # 记录详细历史
                epoch_info = {
                    'epoch': epoch + 1,
                    'train_loss': train_loss,
                    'val_loss': val_loss,
                    'val_mae': val_mae,
                    'learning_rate': new_lr,
                    'timestamp': datetime.now().isoformat(),
                    'epoch_time': time.time() - epoch_start_time
                }
                self.training_history.append(epoch_info)

                # 检查是否是最佳模型
                is_best = val_mae < self.best_mae
                if is_best:
                    improvement = self.best_mae - val_mae
                    self.best_mae = val_mae
                    print(f"🏆 新纪录！MAE改善 {improvement:.3f}岁")

                # 显示轮次总结
                print(f"\n📊 轮次 {epoch+1} 总结:")
                print(f"   🏃 训练损失: {train_loss:.6f}")
                print(f"   🔍 验证损失: {val_loss:.6f}")
                print(f"   🎯 验证MAE: {val_mae:.3f}岁")
                print(f"   📚 学习率: {new_lr:.8f}" + (f" (从 {old_lr:.8f} 调整)" if old_lr != new_lr else ""))
                print(f"   🏆 最佳MAE: {self.best_mae:.3f}岁")

                # 保存检查点
                if (epoch + 1) % save_every == 0 or is_best or epoch == epochs - 1:
                    self.save_checkpoint(epoch + 1, val_mae, is_best)

                # GPU状态
                gpu_info = get_gpu_info()
                if gpu_info:
                    print(f"   🎮 GPU状态: {gpu_info['gpu_utilization']}% 利用率, "
                          f"{gpu_info['memory_used_mb']/1024:.1f}GB/"
                          f"{gpu_info['memory_total_mb']/1024:.1f}GB 内存, "
                          f"{gpu_info['temperature']}°C")

        except KeyboardInterrupt:
            print(f"\n⚠️ 训练被用户中断")
            print(f"💾 保存当前进度...")
            self.save_checkpoint(epoch + 1, val_mae, is_best)
            print(f"✅ 检查点已保存，可以使用 --resume 继续训练")
            return

        total_time = time.time() - training_start_time
        print(f"\n🎉 训练完成！")
        print(f"   总耗时: {total_time/60:.1f}分钟")
        print(f"   最佳MAE: {self.best_mae:.3f}岁")
        print(f"   检查点目录: {self.checkpoint_dir}")

    def export_training_log(self):
        """导出训练日志"""
        log_file = os.path.join(self.checkpoint_dir, 'training_log.json')

        log_data = {
            'training_history': self.training_history,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'val_maes': self.val_maes,
            'best_mae': self.best_mae,
            'export_time': datetime.now().isoformat()
        }

        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)

        print(f"📋 训练日志已导出: {log_file}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='年龄识别模型训练 - 支持断点续训')

    # 基本参数
    parser.add_argument('--epochs', type=int, default=50, help='训练轮次 (默认: 50)')
    parser.add_argument('--batch-size', type=int, default=128, help='批次大小 (默认: 128)')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率 (默认: 0.001)')
    parser.add_argument('--model-type', choices=['simple', 'advanced'], default='simple',
                       help='模型类型 (默认: simple)')

    # 断点续训参数
    parser.add_argument('--resume', action='store_true', help='从最新检查点继续训练')
    parser.add_argument('--checkpoint', type=str, help='指定检查点文件路径')
    parser.add_argument('--checkpoint-dir', type=str, default='checkpoints',
                       help='检查点目录 (默认: checkpoints)')
    parser.add_argument('--save-every', type=int, default=5,
                       help='每N轮次保存检查点 (默认: 5)')

    # 其他参数
    parser.add_argument('--no-amp', action='store_true', help='禁用混合精度训练')
    parser.add_argument('--list-checkpoints', action='store_true', help='列出所有检查点')
    parser.add_argument('--export-log', action='store_true', help='导出训练日志')

    return parser.parse_args()

def main():
    args = parse_args()

    print("=" * 80)
    print("🚀 年龄识别训练系统 - 支持断点续训")
    print("=" * 80)

    # 系统检查
    print("🔍 系统环境:")
    print(f"   Python: {sys.version.split()[0]}")
    print(f"   PyTorch: {torch.__version__}")
    print(f"   CUDA: {'✅' if torch.cuda.is_available() else '❌'}")

    if torch.cuda.is_available():
        gpu_props = torch.cuda.get_device_properties(0)
        print(f"   GPU: {gpu_props.name}")
        print(f"   GPU内存: {gpu_props.total_memory/1e9:.1f}GB")

    # 创建模型
    print(f"\n🧠 模型配置:")
    print(f"   模型类型: {args.model_type}")
    model = AgeRecognitionCNN(model_type=args.model_type)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"   模型参数: {total_params:,}")
    print(f"   模型大小: {total_params * 4 / 1e6:.1f}MB")

    # 创建训练器
    trainer = CheckpointTrainer(
        model=model,
        learning_rate=args.lr,
        use_amp=not args.no_amp,
        checkpoint_dir=args.checkpoint_dir
    )

    # 处理特殊命令
    if args.list_checkpoints:
        trainer.list_checkpoints()
        return

    if args.export_log:
        trainer.export_training_log()
        return

    # 加载检查点
    if args.resume or args.checkpoint:
        checkpoint_path = args.checkpoint if args.checkpoint else None
        if trainer.load_checkpoint(checkpoint_path):
            print(f"✅ 成功加载检查点，继续训练")
        else:
            print(f"⚠️ 无法加载检查点，从头开始训练")

    # 数据预处理
    print(f"\n📊 数据配置:")
    train_transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.RandomCrop((224, 224)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(15),
        transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.2, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # 检查数据集
    if not os.path.exists('data/train_annotations.json'):
        print("❌ 训练数据集不存在，请先运行数据预处理脚本")
        return
    if not os.path.exists('data/val_annotations.json'):
        print("❌ 验证数据集不存在，请先运行数据预处理脚本")
        return

    # 加载数据集
    train_dataset = AgeDataset('data/train', 'data/train_annotations.json', train_transform)
    val_dataset = AgeDataset('data/val', 'data/val_annotations.json', val_transform)

    print(f"   训练集: {len(train_dataset):,} 张图片")
    print(f"   验证集: {len(val_dataset):,} 张图片")
    print(f"   批次大小: {args.batch_size}")

    # 数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=0,
        pin_memory=torch.cuda.is_available()
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=0,
        pin_memory=torch.cuda.is_available()
    )

    print(f"   训练批次: {len(train_loader)}")
    print(f"   验证批次: {len(val_loader)}")

    # 训练配置
    print(f"\n🎯 训练配置:")
    print(f"   总轮次: {args.epochs}")
    print(f"   开始轮次: {trainer.start_epoch + 1}")
    print(f"   学习率: {args.lr}")
    print(f"   混合精度: {'✅' if not args.no_amp and torch.cuda.is_available() else '❌'}")
    print(f"   检查点间隔: 每 {args.save_every} 轮次")
    print(f"   检查点目录: {args.checkpoint_dir}")

    # 开始训练
    trainer.train(train_loader, val_loader, epochs=args.epochs, save_every=args.save_every)

    # 导出训练日志
    trainer.export_training_log()

    print(f"\n🎉 训练系统完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
