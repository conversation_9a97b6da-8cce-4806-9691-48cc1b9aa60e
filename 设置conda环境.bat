@echo off
echo 正在设置Conda环境...

:: 尝试查找Anaconda/Miniconda安装位置
set CONDA_PATHS=^
C:\ProgramData\Anaconda3^
C:\ProgramData\miniconda3^
%USERPROFILE%\Anaconda3^
%USERPROFILE%\miniconda3

for %%p in (%CONDA_PATHS%) do (
    if exist "%%p\Scripts\activate.bat" (
        echo 找到Conda位置: %%p
        call "%%p\Scripts\activate.bat"
        goto :found
    )
)

echo 在常见位置未找到Conda安装。
echo 请安装Anaconda或Miniconda，或手动将其添加到PATH中。
goto :end

:found
echo Conda已激活。现在可以使用conda命令。
echo 要永久设置，请运行: conda init powershell

:end