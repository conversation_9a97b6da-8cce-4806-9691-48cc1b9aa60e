#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版GPU监控训练脚本
显示详细的GPU利用率和训练信息
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import torchvision.transforms as transforms
import os
import json
import time
import numpy as np
import subprocess
import sys

# 启用混合精度训练
from torch.cuda.amp import autocast, GradScaler

def get_gpu_info():
    """获取GPU信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                parts = line.split(', ')
                if len(parts) >= 4:
                    return {
                        'gpu_utilization': float(parts[0]),
                        'memory_used_mb': float(parts[1]),
                        'memory_total_mb': float(parts[2]),
                        'temperature': float(parts[3])
                    }
    except Exception:
        pass
    
    # 备用方案：使用PyTorch信息
    if torch.cuda.is_available():
        return {
            'gpu_utilization': 0,  # 无法获取
            'memory_used_mb': torch.cuda.memory_allocated() / 1e6,
            'memory_total_mb': torch.cuda.get_device_properties(0).total_memory / 1e6,
            'temperature': 0  # 无法获取
        }
    return None

class AgeDataset(Dataset):
    def __init__(self, data_dir, annotations_file, transform=None):
        self.data_dir = data_dir
        self.transform = transform
        
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        self.image_names = list(self.annotations.keys())
        print(f"✅ 加载了 {len(self.image_names)} 张图片")
    
    def __len__(self):
        return len(self.image_names)
    
    def __getitem__(self, idx):
        image_name = self.image_names[idx]
        image_path = os.path.join(self.data_dir, image_name)
        age = self.annotations[image_name]
        
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image, age

class SimpleCNN(nn.Module):
    """简化的CNN模型"""
    def __init__(self):
        super(SimpleCNN, self).__init__()
        
        self.features = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
            
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            nn.Conv2d(128, 256, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            nn.Conv2d(256, 512, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 1)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x.squeeze()

class MonitoredTrainer:
    def __init__(self, model, learning_rate=0.001, use_amp=True):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = model.to(self.device)
        self.use_amp = use_amp and torch.cuda.is_available()
        
        self.optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.criterion = nn.MSELoss()
        self.scaler = GradScaler() if self.use_amp else None
        
        print(f"🚀 训练器初始化完成")
        print(f"   设备: {self.device}")
        print(f"   混合精度: {'✅' if self.use_amp else '❌'}")
        
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            print(f"   GPU: {gpu_props.name}")
            print(f"   GPU内存: {gpu_props.total_memory/1e9:.1f}GB")
    
    def train_epoch(self, train_loader, epoch_num):
        self.model.train()
        total_loss = 0
        num_batches = 0
        samples_processed = 0
        epoch_start_time = time.time()
        
        print(f"\n🏃 开始第 {epoch_num} 轮次训练 (共 {len(train_loader)} 批次)")
        
        for batch_idx, (images, ages) in enumerate(train_loader):
            batch_start_time = time.time()
            
            images = images.to(self.device, non_blocking=True)
            ages = ages.float().to(self.device, non_blocking=True)
            
            self.optimizer.zero_grad()
            
            if self.use_amp:
                with autocast():
                    outputs = self.model(images)
                    loss = self.criterion(outputs, ages)
                
                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(images)
                loss = self.criterion(outputs, ages)
                loss.backward()
                self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            samples_processed += images.size(0)
            
            batch_time = time.time() - batch_start_time
            samples_per_sec = images.size(0) / batch_time
            
            # 每10个批次显示详细信息
            if batch_idx % 10 == 0:
                progress = (batch_idx + 1) / len(train_loader) * 100
                
                # 获取GPU信息
                gpu_info = get_gpu_info()
                
                print(f"  📊 批次 {batch_idx:3d}/{len(train_loader)} ({progress:5.1f}%) | "
                      f"损失: {loss.item():7.4f} | "
                      f"速度: {samples_per_sec:5.1f} 样本/秒")
                
                if gpu_info:
                    print(f"     🎮 GPU利用率: {gpu_info['gpu_utilization']:5.1f}% | "
                          f"GPU内存: {gpu_info['memory_used_mb']/1024:.1f}GB/"
                          f"{gpu_info['memory_total_mb']/1024:.1f}GB "
                          f"({gpu_info['memory_used_mb']/gpu_info['memory_total_mb']*100:5.1f}%) | "
                          f"温度: {gpu_info['temperature']:3.0f}°C")
                
                # PyTorch内存信息
                if torch.cuda.is_available():
                    allocated = torch.cuda.memory_allocated() / 1e9
                    reserved = torch.cuda.memory_reserved() / 1e9
                    print(f"     🔧 PyTorch: {allocated:.2f}GB 已分配 | {reserved:.2f}GB 缓存")
        
        epoch_time = time.time() - epoch_start_time
        avg_samples_per_sec = samples_processed / epoch_time
        
        print(f"\n✅ 轮次 {epoch_num} 训练完成:")
        print(f"   ⏱️  耗时: {epoch_time:.1f}秒")
        print(f"   🚀 平均速度: {avg_samples_per_sec:.1f} 样本/秒")
        print(f"   📈 处理样本: {samples_processed:,}")
        
        return total_loss / num_batches
    
    def validate(self, val_loader, epoch_num):
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_samples = 0
        
        print(f"\n🔍 开始第 {epoch_num} 轮次验证...")
        
        with torch.no_grad():
            for batch_idx, (images, ages) in enumerate(val_loader):
                images = images.to(self.device, non_blocking=True)
                ages = ages.float().to(self.device, non_blocking=True)
                
                if self.use_amp:
                    with autocast():
                        outputs = self.model(images)
                        loss = self.criterion(outputs, ages)
                else:
                    outputs = self.model(images)
                    loss = self.criterion(outputs, ages)
                
                mae = torch.mean(torch.abs(outputs - ages))
                
                total_loss += loss.item() * images.size(0)
                total_mae += mae.item() * images.size(0)
                num_samples += images.size(0)
        
        avg_loss = total_loss / num_samples
        avg_mae = total_mae / num_samples
        
        print(f"✅ 验证完成: 损失 {avg_loss:.4f} | MAE {avg_mae:.2f}岁")
        
        return avg_loss, avg_mae

def main():
    print("=" * 80)
    print("🚀 GPU监控年龄识别训练系统")
    print("=" * 80)
    
    # 系统检查
    print("🔍 系统环境:")
    print(f"   Python: {sys.version.split()[0]}")
    print(f"   PyTorch: {torch.__version__}")
    print(f"   CUDA: {'✅' if torch.cuda.is_available() else '❌'}")
    
    if torch.cuda.is_available():
        gpu_props = torch.cuda.get_device_properties(0)
        print(f"   GPU: {gpu_props.name}")
        print(f"   GPU内存: {gpu_props.total_memory/1e9:.1f}GB")
        
        # 测试GPU监控
        gpu_info = get_gpu_info()
        if gpu_info:
            print(f"   GPU监控: ✅ (利用率: {gpu_info['gpu_utilization']}%)")
        else:
            print(f"   GPU监控: ⚠️ (nvidia-smi不可用)")
    
    # 数据预处理
    train_transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.RandomCrop((224, 224)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 数据集
    print(f"\n📊 加载数据集:")
    train_dataset = AgeDataset('data/train', 'data/train_annotations.json', train_transform)
    val_dataset = AgeDataset('data/val', 'data/val_annotations.json', val_transform)
    
    # 批次大小配置
    batch_size = 128  # 适中的批次大小
    print(f"\n⚙️ 训练配置:")
    print(f"   批次大小: {batch_size}")
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0, pin_memory=True)
    
    print(f"   训练批次: {len(train_loader)}")
    print(f"   验证批次: {len(val_loader)}")
    
    # 模型
    model = SimpleCNN()
    total_params = sum(p.numel() for p in model.parameters())
    print(f"   模型参数: {total_params:,}")
    
    # 训练器
    trainer = MonitoredTrainer(model, learning_rate=0.001, use_amp=True)
    
    # 训练循环
    epochs = 5  # 少量轮次用于测试
    best_mae = float('inf')
    
    print(f"\n🎯 开始训练 {epochs} 轮次...")
    
    for epoch in range(epochs):
        print(f"\n{'='*80}")
        print(f"🔄 轮次 {epoch+1}/{epochs}")
        print(f"{'='*80}")
        
        # 训练
        train_loss = trainer.train_epoch(train_loader, epoch+1)
        
        # 验证
        val_loss, val_mae = trainer.validate(val_loader, epoch+1)
        
        # 保存最佳模型
        if val_mae < best_mae:
            best_mae = val_mae
            torch.save(model.state_dict(), 'best_monitored_age_model.pth')
            print(f"🏆 新纪录！MAE: {val_mae:.2f}岁，已保存模型")
        
        # 最终GPU状态
        gpu_info = get_gpu_info()
        if gpu_info:
            print(f"\n📊 轮次结束GPU状态:")
            print(f"   利用率: {gpu_info['gpu_utilization']}%")
            print(f"   内存使用: {gpu_info['memory_used_mb']/1024:.1f}GB/"
                  f"{gpu_info['memory_total_mb']/1024:.1f}GB")
            print(f"   温度: {gpu_info['temperature']}°C")
    
    print(f"\n🎉 训练完成！最佳MAE: {best_mae:.2f}岁")
    print("=" * 80)

if __name__ == "__main__":
    main()
