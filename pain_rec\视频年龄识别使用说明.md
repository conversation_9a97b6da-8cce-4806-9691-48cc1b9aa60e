# 视频年龄识别系统使用说明

## 📋 功能概述

视频年龄识别系统可以：
- 🎬 **处理视频文件**: 识别视频中所有人物的年龄
- 📹 **实时摄像头识别**: 通过摄像头实时识别年龄
- 🎯 **人脸检测**: 自动检测视频中的人脸
- 📊 **实时统计**: 显示处理速度、检测数量等信息
- 💾 **结果保存**: 可将识别结果保存为新视频

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装OpenCV (如果还没安装)
py -3.12 -m pip install opencv-python
```

### 2. 基本使用

#### 处理视频文件
```bash
# 基本用法 - 处理视频并显示预览
py -3.12 video_age_recognition.py --model checkpoints/best_model.pth --video input_video.mp4

# 保存处理结果
py -3.12 video_age_recognition.py --model checkpoints/best_model.pth --video input_video.mp4 --output output_video.mp4

# 使用高级模型
py -3.12 video_age_recognition.py --model checkpoints/best_model.pth --video input_video.mp4 --model-type advanced
```

#### 摄像头实时识别
```bash
# 使用默认摄像头 (ID: 0)
py -3.12 video_age_recognition.py --model checkpoints/best_model.pth --camera 0

# 使用外接摄像头 (ID: 1)
py -3.12 video_age_recognition.py --model checkpoints/best_model.pth --camera 1
```

## 📝 命令行参数详解

### 必需参数
| 参数 | 说明 | 示例 |
|------|------|------|
| `--model` | 训练好的模型路径 | `checkpoints/best_model.pth` |
| `--video` 或 `--camera` | 输入源 (二选一) | `--video test.mp4` 或 `--camera 0` |

### 可选参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--output` | 无 | 输出视频路径 (仅视频模式) |
| `--model-type` | simple | 模型类型 (simple/advanced) |
| `--device` | 自动 | 计算设备 (cuda/cpu) |
| `--no-preview` | False | 不显示预览窗口 |

## 🎮 操作说明

### 视频模式
- **预览窗口**: 显示实时处理结果
- **按 'q'**: 退出处理
- **进度显示**: 控制台显示处理进度

### 摄像头模式
- **按 'q'**: 退出识别
- **按 's'**: 截图保存当前画面
- **实时显示**: 窗口标题显示FPS等信息

## 📊 界面说明

### 显示元素
1. **人脸框**: 绿色矩形框标识检测到的人脸
2. **年龄标签**: 人脸框上方显示预测年龄
3. **人脸编号**: 人脸框下方显示编号 (#1, #2, ...)
4. **统计信息**: 左上角显示处理统计

### 统计信息面板
```
Frame: 1234          # 当前帧数
Faces: 2             # 当前帧检测到的人脸数
Total Detections: 56 # 总检测人脸数
FPS: 15.3           # 当前处理速度
Avg FPS: 14.8       # 平均处理速度
Device: CUDA        # 使用的计算设备
```

## 🔧 使用示例

### 示例1: 处理会议视频
```bash
# 处理会议录像，识别参会人员年龄
py -3.12 video_age_recognition.py \
    --model checkpoints/best_model.pth \
    --video meeting_record.mp4 \
    --output meeting_with_age.mp4 \
    --model-type advanced
```

### 示例2: 实时年龄识别
```bash
# 启动摄像头进行实时年龄识别
py -3.12 video_age_recognition.py \
    --model checkpoints/best_model.pth \
    --camera 0
```

### 示例3: 批量处理 (需要脚本)
```bash
# 创建批量处理脚本
for video in *.mp4; do
    py -3.12 video_age_recognition.py \
        --model checkpoints/best_model.pth \
        --video "$video" \
        --output "processed_$video" \
        --no-preview
done
```

## 📈 性能优化

### GPU加速
- 系统会自动检测并使用GPU加速
- 确保安装了CUDA版本的PyTorch
- 使用 `--device cuda` 强制使用GPU

### 处理速度优化
1. **降低视频分辨率**: 预处理视频到较低分辨率
2. **使用简单模型**: `--model-type simple` 速度更快
3. **关闭预览**: `--no-preview` 节省显示开销
4. **批量处理**: 避免重复加载模型

### 内存优化
- 处理大视频时，系统会自动管理内存
- 如遇内存不足，可重启脚本继续处理

## 🎯 模型选择指南

### Simple模型
- **优点**: 速度快，内存占用小
- **缺点**: 精度相对较低
- **适用**: 实时应用，资源受限环境
- **预期MAE**: 7-9岁

### Advanced模型
- **优点**: 精度高，识别效果好
- **缺点**: 速度较慢，内存占用大
- **适用**: 离线处理，高精度要求
- **预期MAE**: 5-7岁

## 🔍 故障排除

### 常见问题

#### 1. 模型加载失败
```
❌ 模型加载失败: ...
```
**解决方案**:
- 检查模型文件路径是否正确
- 确认模型文件完整性
- 检查模型类型参数是否匹配

#### 2. 摄像头无法打开
```
❌ 无法打开摄像头: 0
```
**解决方案**:
- 检查摄像头是否被其他程序占用
- 尝试不同的摄像头ID (0, 1, 2...)
- 确认摄像头驱动正常

#### 3. 视频文件无法读取
```
❌ 无法打开视频文件: ...
```
**解决方案**:
- 检查视频文件路径和格式
- 确认OpenCV支持该视频编码
- 尝试转换视频格式

#### 4. 处理速度慢
**优化方案**:
- 使用GPU加速: 确保CUDA可用
- 降低视频分辨率
- 使用simple模型
- 关闭预览窗口

#### 5. 人脸检测效果差
**改进方案**:
- 确保光照充足
- 人脸尽量正面朝向摄像头
- 调整摄像头角度和距离
- 检查视频质量

## 📋 输出文件说明

### 处理后的视频
- **格式**: MP4 (H.264编码)
- **内容**: 原视频 + 人脸框 + 年龄标签 + 统计信息
- **质量**: 与原视频相同

### 截图文件
- **格式**: JPG
- **命名**: `screenshot_YYYYMMDD_HHMMSS.jpg`
- **内容**: 当前帧的识别结果

### 控制台输出
```
✅ 视频处理完成!
   处理帧数: 1500
   检测人脸: 3200
   总耗时: 120.5秒
   平均FPS: 12.4
```

## 🚀 高级用法

### 1. 自定义人脸检测参数
可以修改 `video_age_recognition.py` 中的检测参数：
```python
faces = self.face_cascade.detectMultiScale(
    gray,
    scaleFactor=1.1,      # 缩放因子
    minNeighbors=5,       # 最小邻居数
    minSize=(30, 30),     # 最小人脸尺寸
    flags=cv2.CASCADE_SCALE_IMAGE
)
```

### 2. 集成到其他项目
```python
from video_age_recognition import VideoAgeRecognizer

# 创建识别器
recognizer = VideoAgeRecognizer('checkpoints/best_model.pth')

# 处理单帧
processed_frame = recognizer.process_frame(frame)

# 预测年龄
age = recognizer.predict_age(face_image)
```

### 3. 批量处理脚本
创建 `batch_process.py`:
```python
import os
import glob
from video_age_recognition import VideoAgeRecognizer

recognizer = VideoAgeRecognizer('checkpoints/best_model.pth')

for video_file in glob.glob('*.mp4'):
    output_file = f'processed_{video_file}'
    recognizer.process_video(video_file, output_file, show_preview=False)
    print(f'完成: {video_file} -> {output_file}')
```

## 📞 技术支持

如遇问题，请检查：
1. 所有依赖是否正确安装
2. 模型文件是否存在且完整
3. 输入视频格式是否支持
4. 系统资源是否充足

**祝您使用愉快！** 🎉
