#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU内存使用分析和优化脚本
分析PyTorch在年龄识别训练中的GPU内存使用模式
"""

import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import time
import gc

def analyze_gpu_memory():
    """分析GPU内存状态"""
    if not torch.cuda.is_available():
        print("CUDA不可用，无法进行GPU内存分析")
        return
    
    print("=" * 60)
    print("GPU内存详细分析")
    print("=" * 60)
    
    # 基本GPU信息
    gpu_props = torch.cuda.get_device_properties(0)
    total_memory = gpu_props.total_memory / 1e9
    
    print(f"GPU型号: {gpu_props.name}")
    print(f"GPU总内存: {total_memory:.2f}GB")
    print(f"CUDA计算能力: {gpu_props.major}.{gpu_props.minor}")
    print(f"多处理器数量: {gpu_props.multi_processor_count}")
    
    # 当前内存状态
    print(f"\n当前内存状态:")
    print(f"已分配内存: {torch.cuda.memory_allocated()/1e9:.3f}GB")
    print(f"缓存内存: {torch.cuda.memory_reserved()/1e9:.3f}GB")
    print(f"最大已分配: {torch.cuda.max_memory_allocated()/1e9:.3f}GB")
    print(f"最大缓存: {torch.cuda.max_memory_reserved()/1e9:.3f}GB")
    
    return total_memory

def test_batch_sizes(model_class, input_shape=(3, 224, 224)):
    """测试不同批次大小的GPU内存使用"""
    print(f"\n{'='*60}")
    print("批次大小内存使用测试")
    print(f"{'='*60}")
    
    batch_sizes = [16, 32, 64, 96, 128, 160, 192, 224, 256]
    results = []
    
    for batch_size in batch_sizes:
        try:
            # 清理GPU内存
            torch.cuda.empty_cache()
            gc.collect()
            
            # 创建模型和数据
            model = model_class().cuda()
            dummy_input = torch.randn(batch_size, *input_shape).cuda()
            dummy_target = torch.randn(batch_size).cuda()
            
            # 前向传播
            torch.cuda.synchronize()
            start_mem = torch.cuda.memory_allocated()
            
            output = model(dummy_input)
            loss = nn.MSELoss()(output, dummy_target)
            
            # 反向传播
            loss.backward()
            
            torch.cuda.synchronize()
            peak_mem = torch.cuda.max_memory_allocated()
            
            # 记录结果
            memory_used = peak_mem / 1e9
            memory_per_sample = memory_used / batch_size * 1000  # MB per sample
            
            results.append({
                'batch_size': batch_size,
                'memory_gb': memory_used,
                'memory_per_sample_mb': memory_per_sample,
                'success': True
            })
            
            print(f"批次大小 {batch_size:3d}: {memory_used:.2f}GB ({memory_per_sample:.1f}MB/样本)")
            
            # 清理
            del model, dummy_input, dummy_target, output, loss
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"批次大小 {batch_size:3d}: 内存不足 (OOM)")
                results.append({
                    'batch_size': batch_size,
                    'memory_gb': None,
                    'memory_per_sample_mb': None,
                    'success': False
                })
                break
            else:
                raise e
    
    return results

def recommend_optimal_batch_size(results, total_memory_gb):
    """推荐最优批次大小"""
    print(f"\n{'='*60}")
    print("批次大小优化建议")
    print(f"{'='*60}")
    
    successful_results = [r for r in results if r['success']]
    
    if not successful_results:
        print("没有成功的测试结果")
        return 32
    
    # 找到使用80%GPU内存的最大批次大小
    target_memory = total_memory_gb * 0.8
    
    optimal_batch = 32
    for result in successful_results:
        if result['memory_gb'] <= target_memory:
            optimal_batch = result['batch_size']
        else:
            break
    
    print(f"GPU总内存: {total_memory_gb:.1f}GB")
    print(f"建议使用内存: {target_memory:.1f}GB (80%)")
    print(f"推荐批次大小: {optimal_batch}")
    
    # 显示内存效率分析
    if successful_results:
        best_result = [r for r in successful_results if r['batch_size'] == optimal_batch][0]
        print(f"预期内存使用: {best_result['memory_gb']:.2f}GB")
        print(f"内存利用率: {best_result['memory_gb']/total_memory_gb*100:.1f}%")
        print(f"每样本内存: {best_result['memory_per_sample_mb']:.1f}MB")
    
    return optimal_batch

def analyze_memory_patterns():
    """分析PyTorch内存分配模式"""
    print(f"\n{'='*60}")
    print("PyTorch内存分配模式分析")
    print(f"{'='*60}")
    
    print("1. PyTorch内存管理机制:")
    print("   - PyTorch使用内存池(memory pool)来管理GPU内存")
    print("   - 'memory_allocated()' 显示实际使用的内存")
    print("   - 'memory_reserved()' 显示PyTorch缓存的内存池大小")
    print("   - 内存池避免频繁的malloc/free操作，提高性能")
    
    print("\n2. 低内存使用率的可能原因:")
    print("   - 模型参数较少(SimpleCNN约172万参数)")
    print("   - 批次大小相对保守(64)")
    print("   - 输入图像分辨率适中(224x224)")
    print("   - PyTorch内存池预分配策略")
    
    print("\n3. 内存显示说明:")
    print("   - '0.05GB / 2.42GB' 表示:")
    print("     * 0.05GB: 当前实际使用的内存")
    print("     * 2.42GB: PyTorch分配的内存池大小")
    print("   - 内存池大小会根据需要动态增长")
    print("   - 即使模型不大，内存池也会保留一定空间")

def create_optimized_model():
    """创建内存优化的模型"""
    class OptimizedCNN(nn.Module):
        def __init__(self, use_mixed_precision=True):
            super(OptimizedCNN, self).__init__()
            self.use_mixed_precision = use_mixed_precision
            
            # 更深的网络以更好利用GPU
            self.features = nn.Sequential(
                # 第一组卷积块
                nn.Conv2d(3, 64, 7, 2, 3),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(3, 2, 1),
                
                # 第二组卷积块
                nn.Conv2d(64, 128, 3, 1, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 128, 3, 1, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(2, 2),
                
                # 第三组卷积块
                nn.Conv2d(128, 256, 3, 1, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 256, 3, 1, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(2, 2),
                
                # 第四组卷积块
                nn.Conv2d(256, 512, 3, 1, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 512, 3, 1, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(2, 2),
                
                # 第五组卷积块
                nn.Conv2d(512, 1024, 3, 1, 1),
                nn.BatchNorm2d(1024),
                nn.ReLU(inplace=True),
                nn.Conv2d(1024, 1024, 3, 1, 1),
                nn.BatchNorm2d(1024),
                nn.ReLU(inplace=True),
                nn.AdaptiveAvgPool2d((1, 1))
            )
            
            # 更大的分类器
            self.classifier = nn.Sequential(
                nn.Dropout(0.5),
                nn.Linear(1024, 2048),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(2048, 1024),
                nn.ReLU(inplace=True),
                nn.Dropout(0.3),
                nn.Linear(1024, 512),
                nn.ReLU(inplace=True),
                nn.Linear(512, 1)
            )
        
        def forward(self, x):
            x = self.features(x)
            x = torch.flatten(x, 1)
            x = self.classifier(x)
            return x.squeeze()
    
    return OptimizedCNN

def main():
    print("GPU内存使用分析和优化")
    
    # 1. 基本GPU内存分析
    total_memory = analyze_gpu_memory()
    
    # 2. 分析内存分配模式
    analyze_memory_patterns()
    
    # 3. 导入原始模型进行测试
    from train_age_gpu import SimpleCNN
    
    print(f"\n{'='*60}")
    print("原始SimpleCNN模型内存测试")
    print(f"{'='*60}")
    
    # 计算模型参数
    model = SimpleCNN()
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数数量: {total_params:,}")
    print(f"模型大小估计: {total_params * 4 / 1e6:.1f}MB (float32)")
    
    # 测试不同批次大小
    results = test_batch_sizes(SimpleCNN)
    
    # 推荐最优批次大小
    optimal_batch = recommend_optimal_batch_size(results, total_memory)
    
    # 4. 测试优化模型
    print(f"\n{'='*60}")
    print("优化后模型内存测试")
    print(f"{'='*60}")
    
    OptimizedCNN = create_optimized_model()
    model_opt = OptimizedCNN()
    total_params_opt = sum(p.numel() for p in model_opt.parameters())
    print(f"优化模型参数数量: {total_params_opt:,}")
    print(f"优化模型大小估计: {total_params_opt * 4 / 1e6:.1f}MB (float32)")
    
    results_opt = test_batch_sizes(OptimizedCNN)
    optimal_batch_opt = recommend_optimal_batch_size(results_opt, total_memory)
    
    # 5. 总结和建议
    print(f"\n{'='*60}")
    print("优化建议总结")
    print(f"{'='*60}")
    
    print("1. 批次大小优化:")
    print(f"   - 原始模型推荐批次大小: {optimal_batch}")
    print(f"   - 优化模型推荐批次大小: {optimal_batch_opt}")
    print(f"   - 当前使用批次大小: 64 (可以增加)")
    
    print("\n2. 内存使用率低的原因:")
    print("   - 模型相对简单，参数量较少")
    print("   - 批次大小保守，未充分利用GPU内存")
    print("   - PyTorch内存池机制导致显示的缓存内存较小")
    
    print("\n3. 性能优化建议:")
    print(f"   - 增加批次大小到 {optimal_batch} 或更高")
    print("   - 考虑使用更深的网络架构")
    print("   - 启用混合精度训练(AMP)节省内存")
    print("   - 使用数据并行(DataParallel)如果有多GPU")
    
    print("\n4. 训练效率影响:")
    print("   - 低内存使用率不会直接影响训练准确性")
    print("   - 但可能导致GPU利用率不足，训练速度未达到最优")
    print("   - 增加批次大小可以提高训练稳定性和收敛速度")

if __name__ == "__main__":
    main()
