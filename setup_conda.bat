@echo off
echo Setting up Conda environment...

:: Try to find Anaconda/Miniconda installation
set CONDA_PATHS=^
D:\ProgramData\anaconda3^
D:\ProgramData\miniconda3^
%USERPROFILE%\Anaconda3^
%USERPROFILE%\miniconda3

for %%p in (%CONDA_PATHS%) do (
    if exist "%%p\Scripts\activate.bat" (
        echo Found Conda at: %%p
        call "%%p\Scripts\activate.bat"
        goto :found
    )
)

echo Conda installation not found in common locations.
echo Please install Anaconda or Miniconda, or manually add it to your PATH.
goto :end

:found
echo Conda is now activated. You can use conda commands.
echo To make this permanent, run: conda init powershell

:end