#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年龄识别训练系统快速启动脚本
提供交互式菜单，简化操作
"""

import os
import sys
import subprocess
import json
from datetime import datetime

def print_banner():
    """打印欢迎横幅"""
    print("=" * 80)
    print("🚀 年龄识别训练系统 - 快速启动")
    print("=" * 80)
    print("功能特性:")
    print("  ✅ 断点续训 - 支持训练中断后继续")
    print("  ✅ GPU监控 - 实时显示GPU利用率和内存")
    print("  ✅ 混合精度 - 自动启用AMP加速训练")
    print("  ✅ 智能检查点 - 自动保存最佳模型")
    print("  ✅ 详细日志 - 完整的训练历史记录")
    print("=" * 80)

def check_environment():
    """检查环境和依赖"""
    print("\n🔍 环境检查:")
    
    # 检查Python版本
    python_version = sys.version.split()[0]
    print(f"   Python版本: {python_version}")
    
    # 检查PyTorch
    try:
        import torch
        print(f"   PyTorch版本: {torch.__version__}")
        print(f"   CUDA可用: {'✅' if torch.cuda.is_available() else '❌'}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"   GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        
        return True
    except ImportError:
        print("   ❌ PyTorch未安装")
        return False

def check_dataset():
    """检查数据集"""
    print("\n📁 数据集检查:")
    
    required_files = [
        'data/train_annotations.json',
        'data/val_annotations.json'
    ]
    
    required_dirs = [
        'data/train',
        'data/val'
    ]
    
    all_good = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (缺失)")
            all_good = False
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            file_count = len([f for f in os.listdir(dir_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
            print(f"   ✅ {dir_path} ({file_count} 张图片)")
        else:
            print(f"   ❌ {dir_path} (缺失)")
            all_good = False
    
    return all_good

def list_checkpoints():
    """列出检查点"""
    checkpoint_dir = 'checkpoints'
    if not os.path.exists(checkpoint_dir):
        print("📁 检查点目录不存在")
        return []
    
    checkpoints = []
    for file in os.listdir(checkpoint_dir):
        if file.endswith('.pth'):
            file_path = os.path.join(checkpoint_dir, file)
            try:
                import torch
                checkpoint = torch.load(file_path, map_location='cpu')
                checkpoints.append({
                    'file': file,
                    'path': file_path,
                    'epoch': checkpoint.get('epoch', 0),
                    'mae': checkpoint.get('val_mae', float('inf')),
                    'timestamp': checkpoint.get('timestamp', '未知')
                })
            except:
                continue
    
    checkpoints.sort(key=lambda x: x['epoch'])
    
    if checkpoints:
        print(f"\n📋 可用检查点 ({len(checkpoints)} 个):")
        for i, cp in enumerate(checkpoints):
            print(f"   {i+1}. {cp['file']}: 轮次 {cp['epoch']}, MAE {cp['mae']:.3f}岁")
    else:
        print("\n📋 暂无可用检查点")
    
    return checkpoints

def get_user_choice(prompt, choices):
    """获取用户选择"""
    while True:
        try:
            choice = input(f"{prompt} ({'/'.join(choices)}): ").strip().lower()
            if choice in choices:
                return choice
            print(f"请输入有效选择: {'/'.join(choices)}")
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            sys.exit(0)

def get_user_input(prompt, default=None, input_type=str):
    """获取用户输入"""
    while True:
        try:
            if default is not None:
                user_input = input(f"{prompt} (默认: {default}): ").strip()
                if not user_input:
                    return default
            else:
                user_input = input(f"{prompt}: ").strip()
            
            if input_type == int:
                return int(user_input)
            elif input_type == float:
                return float(user_input)
            else:
                return user_input
                
        except ValueError:
            print(f"请输入有效的{input_type.__name__}值")
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            sys.exit(0)

def build_command(config):
    """构建训练命令"""
    cmd = ["py", "-3.12", "train_age_checkpoint.py"]
    
    # 基本参数
    cmd.extend(["--epochs", str(config['epochs'])])
    cmd.extend(["--batch-size", str(config['batch_size'])])
    cmd.extend(["--lr", str(config['lr'])])
    cmd.extend(["--model-type", config['model_type']])
    cmd.extend(["--save-every", str(config['save_every'])])
    
    # 断点续训
    if config.get('resume'):
        cmd.append("--resume")
    elif config.get('checkpoint'):
        cmd.extend(["--checkpoint", config['checkpoint']])
    
    # 其他选项
    if config.get('no_amp'):
        cmd.append("--no-amp")
    
    if config.get('checkpoint_dir'):
        cmd.extend(["--checkpoint-dir", config['checkpoint_dir']])
    
    return cmd

def main_menu():
    """主菜单"""
    while True:
        print("\n" + "=" * 50)
        print("📋 主菜单")
        print("=" * 50)
        print("1. 🚀 开始新训练")
        print("2. 🔄 断点续训")
        print("3. 📊 查看检查点")
        print("4. 📋 导出训练日志")
        print("5. 🔧 系统检查")
        print("6. 📖 查看帮助")
        print("7. 👋 退出")
        
        choice = get_user_choice("请选择操作", ['1', '2', '3', '4', '5', '6', '7'])
        
        if choice == '1':
            new_training()
        elif choice == '2':
            resume_training()
        elif choice == '3':
            view_checkpoints()
        elif choice == '4':
            export_logs()
        elif choice == '5':
            system_check()
        elif choice == '6':
            show_help()
        elif choice == '7':
            print("\n👋 再见！")
            break

def new_training():
    """新训练配置"""
    print("\n🚀 配置新训练")
    print("-" * 30)
    
    config = {}
    
    # 基本参数
    config['epochs'] = get_user_input("训练轮次", 50, int)
    config['batch_size'] = get_user_input("批次大小", 128, int)
    config['lr'] = get_user_input("学习率", 0.001, float)
    
    # 模型类型
    model_choice = get_user_choice("模型类型 (simple=简单, advanced=高级)", ['simple', 'advanced'])
    config['model_type'] = model_choice
    
    # 保存间隔
    config['save_every'] = get_user_input("检查点保存间隔(轮次)", 5, int)
    
    # 高级选项
    advanced = get_user_choice("配置高级选项", ['y', 'n'])
    if advanced == 'y':
        config['no_amp'] = get_user_choice("禁用混合精度训练", ['y', 'n']) == 'y'
        config['checkpoint_dir'] = get_user_input("检查点目录", "checkpoints")
    
    # 显示配置
    print(f"\n📋 训练配置:")
    print(f"   轮次: {config['epochs']}")
    print(f"   批次大小: {config['batch_size']}")
    print(f"   学习率: {config['lr']}")
    print(f"   模型类型: {config['model_type']}")
    print(f"   保存间隔: {config['save_every']} 轮次")
    
    # 确认启动
    confirm = get_user_choice("开始训练", ['y', 'n'])
    if confirm == 'y':
        cmd = build_command(config)
        print(f"\n🚀 启动训练...")
        print(f"命令: {' '.join(cmd)}")
        subprocess.run(cmd)

def resume_training():
    """断点续训"""
    print("\n🔄 断点续训")
    print("-" * 30)
    
    checkpoints = list_checkpoints()
    
    if not checkpoints:
        print("❌ 没有可用的检查点")
        return
    
    print("\n选择恢复方式:")
    print("1. 从最新检查点继续")
    print("2. 选择特定检查点")
    
    choice = get_user_choice("请选择", ['1', '2'])
    
    config = {}
    
    if choice == '1':
        config['resume'] = True
    else:
        print("\n选择检查点:")
        for i, cp in enumerate(checkpoints):
            print(f"   {i+1}. {cp['file']} (轮次 {cp['epoch']}, MAE {cp['mae']:.3f}岁)")
        
        while True:
            try:
                idx = int(input("请输入检查点编号: ")) - 1
                if 0 <= idx < len(checkpoints):
                    config['checkpoint'] = checkpoints[idx]['path']
                    break
                else:
                    print("无效编号")
            except ValueError:
                print("请输入数字")
    
    # 其他参数
    config['epochs'] = get_user_input("总轮次 (包括已完成的)", 100, int)
    
    # 确认启动
    confirm = get_user_choice("开始续训", ['y', 'n'])
    if confirm == 'y':
        cmd = build_command(config)
        print(f"\n🔄 启动续训...")
        print(f"命令: {' '.join(cmd)}")
        subprocess.run(cmd)

def view_checkpoints():
    """查看检查点"""
    print("\n📊 检查点详情")
    print("-" * 30)
    
    checkpoints = list_checkpoints()
    
    if checkpoints:
        # 显示统计信息
        best_checkpoint = min(checkpoints, key=lambda x: x['mae'])
        latest_checkpoint = max(checkpoints, key=lambda x: x['epoch'])
        
        print(f"\n📈 统计信息:")
        print(f"   检查点总数: {len(checkpoints)}")
        print(f"   最佳MAE: {best_checkpoint['mae']:.3f}岁 ({best_checkpoint['file']})")
        print(f"   最新轮次: {latest_checkpoint['epoch']} ({latest_checkpoint['file']})")

def export_logs():
    """导出训练日志"""
    print("\n📋 导出训练日志")
    print("-" * 30)
    
    cmd = ["py", "-3.12", "train_age_checkpoint.py", "--export-log"]
    print("🚀 导出日志...")
    subprocess.run(cmd)

def system_check():
    """系统检查"""
    print("\n🔧 系统检查")
    print("-" * 30)
    
    env_ok = check_environment()
    data_ok = check_dataset()
    
    print(f"\n📊 检查结果:")
    print(f"   环境状态: {'✅ 正常' if env_ok else '❌ 异常'}")
    print(f"   数据集状态: {'✅ 正常' if data_ok else '❌ 异常'}")
    
    if env_ok and data_ok:
        print("🎉 系统准备就绪，可以开始训练！")
    else:
        print("⚠️ 请解决上述问题后再开始训练")

def show_help():
    """显示帮助"""
    print("\n📖 帮助信息")
    print("-" * 30)
    print("🚀 快速开始:")
    print("   1. 选择 '开始新训练' 进行首次训练")
    print("   2. 训练过程中可以按 Ctrl+C 安全中断")
    print("   3. 使用 '断点续训' 从中断处继续")
    print()
    print("📋 重要文件:")
    print("   - train_age_checkpoint.py: 主训练脚本")
    print("   - checkpoints/: 检查点目录")
    print("   - 训练系统操作手册.md: 详细文档")
    print()
    print("🎮 GPU监控:")
    print("   训练过程中会显示实时GPU利用率、内存使用和温度")
    print()
    print("💾 检查点说明:")
    print("   - latest_checkpoint.pth: 最新检查点")
    print("   - best_model.pth: 最佳模型")
    print("   - checkpoint_epoch_*.pth: 定期检查点")
    print()
    print("📞 如需详细帮助，请查看 '训练系统操作手册.md'")

def main():
    """主函数"""
    print_banner()
    
    # 基本检查
    if not check_environment():
        print("\n❌ 环境检查失败，请安装必要依赖")
        return
    
    if not check_dataset():
        print("\n⚠️ 数据集检查失败，但仍可继续操作")
    
    # 进入主菜单
    main_menu()

if __name__ == "__main__":
    main()
