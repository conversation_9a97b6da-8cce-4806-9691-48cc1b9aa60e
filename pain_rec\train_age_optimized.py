#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU内存优化的年龄识别训练脚本
基于内存分析结果进行优化
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import torchvision.transforms as transforms
import os
import json
import time
import matplotlib.pyplot as plt
import numpy as np
import psutil
import threading
import subprocess
import sys

# 启用混合精度训练
from torch.cuda.amp import autocast, GradScaler

class GPUMonitor:
    """GPU利用率和内存监控器"""
    def __init__(self):
        self.monitoring = False
        self.gpu_utilization = []
        self.gpu_memory_used = []
        self.gpu_memory_total = []
        self.timestamps = []

    def start_monitoring(self):
        """开始监控GPU状态"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 使用nvidia-smi获取GPU信息
                result = subprocess.run([
                    'nvidia-smi',
                    '--query-gpu=utilization.gpu,memory.used,memory.total',
                    '--format=csv,noheader,nounits'
                ], capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        parts = line.split(', ')
                        if len(parts) >= 3:
                            gpu_util = float(parts[0])
                            mem_used = float(parts[1])
                            mem_total = float(parts[2])

                            self.gpu_utilization.append(gpu_util)
                            self.gpu_memory_used.append(mem_used)
                            self.gpu_memory_total.append(mem_total)
                            self.timestamps.append(time.time())
                            break

            except Exception:
                # 如果nvidia-smi不可用，使用PyTorch的内存信息
                if torch.cuda.is_available():
                    mem_used = torch.cuda.memory_allocated() / 1e6  # MB
                    mem_total = torch.cuda.get_device_properties(0).total_memory / 1e6  # MB

                    self.gpu_utilization.append(0)  # 无法获取利用率
                    self.gpu_memory_used.append(mem_used)
                    self.gpu_memory_total.append(mem_total)
                    self.timestamps.append(time.time())

            time.sleep(1)  # 每秒监控一次

    def get_current_stats(self):
        """获取当前GPU状态"""
        if not self.gpu_utilization:
            return None

        return {
            'gpu_utilization': self.gpu_utilization[-1] if self.gpu_utilization else 0,
            'memory_used_mb': self.gpu_memory_used[-1] if self.gpu_memory_used else 0,
            'memory_total_mb': self.gpu_memory_total[-1] if self.gpu_memory_total else 0,
            'memory_usage_percent': (self.gpu_memory_used[-1] / self.gpu_memory_total[-1] * 100) if self.gpu_memory_used and self.gpu_memory_total else 0
        }

    def get_average_stats(self, last_n_seconds=60):
        """获取最近N秒的平均统计"""
        if not self.timestamps:
            return None

        current_time = time.time()
        recent_indices = [i for i, t in enumerate(self.timestamps) if current_time - t <= last_n_seconds]

        if not recent_indices:
            return None

        recent_gpu_util = [self.gpu_utilization[i] for i in recent_indices]
        recent_mem_used = [self.gpu_memory_used[i] for i in recent_indices]
        recent_mem_total = [self.gpu_memory_total[i] for i in recent_indices]

        return {
            'avg_gpu_utilization': np.mean(recent_gpu_util),
            'avg_memory_used_mb': np.mean(recent_mem_used),
            'avg_memory_total_mb': np.mean(recent_mem_total),
            'avg_memory_usage_percent': np.mean(recent_mem_used) / np.mean(recent_mem_total) * 100 if recent_mem_total else 0
        }

class AgeDataset(Dataset):
    def __init__(self, data_dir, annotations_file, transform=None):
        self.data_dir = data_dir
        self.transform = transform
        
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        self.image_names = list(self.annotations.keys())
        print(f"加载了 {len(self.image_names)} 张图片")
    
    def __len__(self):
        return len(self.image_names)
    
    def __getitem__(self, idx):
        image_name = self.image_names[idx]
        image_path = os.path.join(self.data_dir, image_name)
        age = self.annotations[image_name]
        
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image, age

class OptimizedCNN(nn.Module):
    """内存优化的CNN模型 - 更深更强"""
    def __init__(self):
        super(OptimizedCNN, self).__init__()
        
        self.features = nn.Sequential(
            # 第一组卷积块 (224->112)
            nn.Conv2d(3, 64, 7, 2, 3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(3, 2, 1),
            
            # 第二组卷积块 (56->28)
            nn.Conv2d(64, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第三组卷积块 (28->14)
            nn.Conv2d(128, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第四组卷积块 (14->7)
            nn.Conv2d(256, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            # 第五组卷积块 (7->1)
            nn.Conv2d(512, 1024, 3, 1, 1),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 1024, 3, 1, 1),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(1024, 2048),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(2048, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 1)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x.squeeze()

class OptimizedTrainer:
    def __init__(self, model, learning_rate=0.001, use_amp=True):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = model.to(self.device)
        self.use_amp = use_amp and torch.cuda.is_available()

        self.optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', patience=5, factor=0.5)
        self.criterion = nn.MSELoss()

        # 混合精度训练
        self.scaler = GradScaler() if self.use_amp else None

        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.val_maes = []
        self.epoch_times = []
        self.samples_per_second = []

        # GPU监控器
        self.gpu_monitor = GPUMonitor() if torch.cuda.is_available() else None

        # 显示初始化信息
        self._print_initialization_info()

    def _print_initialization_info(self):
        """打印详细的初始化信息"""
        print("=" * 80)
        print("🚀 优化训练器初始化")
        print("=" * 80)

        print(f"📱 设备信息:")
        print(f"   使用设备: {self.device}")
        print(f"   混合精度训练: {'✅ 启用' if self.use_amp else '❌ 禁用'}")

        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            print(f"   GPU型号: {gpu_props.name}")
            print(f"   GPU内存: {gpu_props.total_memory/1e9:.1f}GB")
            print(f"   CUDA计算能力: {gpu_props.major}.{gpu_props.minor}")
            print(f"   多处理器数量: {gpu_props.multi_processor_count}")

        print(f"\n🧠 模型信息:")
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        model_size_mb = total_params * 4 / 1e6  # float32

        print(f"   总参数数量: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        print(f"   模型大小: {model_size_mb:.1f}MB")

        print(f"\n⚙️ 优化器信息:")
        print(f"   优化器: {type(self.optimizer).__name__}")
        print(f"   学习率: {self.optimizer.param_groups[0]['lr']}")
        print(f"   权重衰减: {self.optimizer.param_groups[0]['weight_decay']}")

        print(f"\n📊 系统信息:")
        print(f"   CPU核心数: {psutil.cpu_count()}")
        print(f"   系统内存: {psutil.virtual_memory().total/1e9:.1f}GB")
        print(f"   可用内存: {psutil.virtual_memory().available/1e9:.1f}GB")

        print("=" * 80)
    
    def train_epoch(self, train_loader, epoch_num):
        self.model.train()
        total_loss = 0
        num_batches = 0
        samples_processed = 0
        epoch_start_time = time.time()

        # 开始GPU监控
        if self.gpu_monitor:
            self.gpu_monitor.start_monitoring()

        print(f"\n🏃 开始第 {epoch_num} 轮次训练...")
        print(f"📦 总批次数: {len(train_loader)}")

        for batch_idx, (images, ages) in enumerate(train_loader):
            batch_start_time = time.time()

            images = images.to(self.device, non_blocking=True)
            ages = ages.float().to(self.device, non_blocking=True)

            self.optimizer.zero_grad()

            if self.use_amp:
                with autocast():
                    outputs = self.model(images)
                    loss = self.criterion(outputs, ages)

                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(images)
                loss = self.criterion(outputs, ages)
                loss.backward()
                self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1
            samples_processed += images.size(0)

            batch_time = time.time() - batch_start_time
            samples_per_sec = images.size(0) / batch_time

            # 详细的批次信息显示
            if batch_idx % 20 == 0:
                elapsed_time = time.time() - epoch_start_time
                progress = (batch_idx + 1) / len(train_loader) * 100
                eta = elapsed_time / (batch_idx + 1) * (len(train_loader) - batch_idx - 1)

                print(f"  📊 批次 {batch_idx:3d}/{len(train_loader)} ({progress:5.1f}%) | "
                      f"损失: {loss.item():7.4f} | "
                      f"速度: {samples_per_sec:5.1f} 样本/秒 | "
                      f"ETA: {eta:5.1f}秒")

                # GPU状态信息
                if self.gpu_monitor:
                    gpu_stats = self.gpu_monitor.get_current_stats()
                    if gpu_stats:
                        print(f"     🎮 GPU利用率: {gpu_stats['gpu_utilization']:5.1f}% | "
                              f"GPU内存: {gpu_stats['memory_used_mb']/1024:.1f}GB/"
                              f"{gpu_stats['memory_total_mb']/1024:.1f}GB "
                              f"({gpu_stats['memory_usage_percent']:5.1f}%)")

                # PyTorch内存信息
                if torch.cuda.is_available():
                    allocated = torch.cuda.memory_allocated() / 1e9
                    reserved = torch.cuda.memory_reserved() / 1e9
                    print(f"     🔧 PyTorch内存: {allocated:.2f}GB 已分配 | {reserved:.2f}GB 缓存")

        # 停止GPU监控
        if self.gpu_monitor:
            self.gpu_monitor.stop_monitoring()

        epoch_time = time.time() - epoch_start_time
        avg_samples_per_sec = samples_processed / epoch_time

        print(f"\n✅ 轮次 {epoch_num} 训练完成:")
        print(f"   ⏱️  总耗时: {epoch_time:.1f}秒")
        print(f"   🚀 平均速度: {avg_samples_per_sec:.1f} 样本/秒")
        print(f"   📈 处理样本: {samples_processed:,}")

        return total_loss / num_batches, epoch_time, avg_samples_per_sec
    
    def validate(self, val_loader, epoch_num):
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_samples = 0
        val_start_time = time.time()

        print(f"\n🔍 开始第 {epoch_num} 轮次验证...")

        with torch.no_grad():
            for batch_idx, (images, ages) in enumerate(val_loader):
                images = images.to(self.device, non_blocking=True)
                ages = ages.float().to(self.device, non_blocking=True)

                if self.use_amp:
                    with autocast():
                        outputs = self.model(images)
                        loss = self.criterion(outputs, ages)
                else:
                    outputs = self.model(images)
                    loss = self.criterion(outputs, ages)

                mae = torch.mean(torch.abs(outputs - ages))

                total_loss += loss.item() * images.size(0)
                total_mae += mae.item() * images.size(0)
                num_samples += images.size(0)

                if batch_idx % 10 == 0:
                    progress = (batch_idx + 1) / len(val_loader) * 100
                    print(f"  📊 验证批次 {batch_idx:3d}/{len(val_loader)} ({progress:5.1f}%) | "
                          f"损失: {loss.item():7.4f} | MAE: {mae.item():6.2f}岁")

        val_time = time.time() - val_start_time
        avg_loss = total_loss / num_samples
        avg_mae = total_mae / num_samples

        print(f"\n✅ 验证完成:")
        print(f"   ⏱️  验证耗时: {val_time:.1f}秒")
        print(f"   📊 验证样本: {num_samples:,}")
        print(f"   📉 平均损失: {avg_loss:.4f}")
        print(f"   🎯 平均MAE: {avg_mae:.2f}岁")

        return avg_loss, avg_mae
    
    def train(self, train_loader, val_loader, epochs=50):
        print(f"\n🚀 开始优化训练 {epochs} 个轮次...")
        print("=" * 80)

        best_mae = float('inf')
        best_epoch = 0
        training_start_time = time.time()

        # 训练统计
        total_samples_processed = 0

        for epoch in range(epochs):
            epoch_start_time = time.time()

            print(f"\n{'='*80}")
            print(f"🔄 轮次 {epoch+1}/{epochs}")
            print(f"{'='*80}")

            # 训练
            train_loss, train_time, train_speed = self.train_epoch(train_loader, epoch+1)

            # 验证
            val_loss, val_mae = self.validate(val_loader, epoch+1)

            # 学习率调度
            old_lr = self.optimizer.param_groups[0]['lr']
            self.scheduler.step(val_loss)
            new_lr = self.optimizer.param_groups[0]['lr']

            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_maes.append(val_mae)
            self.epoch_times.append(train_time)
            self.samples_per_second.append(train_speed)

            epoch_total_time = time.time() - epoch_start_time
            total_samples_processed += len(train_loader.dataset)

            # 轮次总结
            print(f"\n📊 轮次 {epoch+1} 总结:")
            print(f"   🏃 训练损失: {train_loss:.6f}")
            print(f"   🔍 验证损失: {val_loss:.6f}")
            print(f"   🎯 验证MAE: {val_mae:.3f}岁")
            print(f"   📚 学习率: {new_lr:.8f}" + (f" (从 {old_lr:.8f} 调整)" if old_lr != new_lr else ""))
            print(f"   ⏱️  轮次总耗时: {epoch_total_time:.1f}秒")
            print(f"   🚀 训练速度: {train_speed:.1f} 样本/秒")

            # 保存最佳模型
            if val_mae < best_mae:
                improvement = best_mae - val_mae
                best_mae = val_mae
                best_epoch = epoch + 1
                torch.save(self.model.state_dict(), 'best_optimized_age_model.pth')
                print(f"   🏆 新纪录！MAE改善 {improvement:.3f}岁，保存最佳模型")
            else:
                print(f"   📈 当前最佳MAE: {best_mae:.3f}岁 (轮次 {best_epoch})")

            # 详细的GPU和系统状态
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated()/1e9
                reserved = torch.cuda.memory_reserved()/1e9
                max_allocated = torch.cuda.max_memory_allocated()/1e9
                total_memory = torch.cuda.get_device_properties(0).total_memory/1e9

                print(f"   🎮 GPU内存状态:")
                print(f"      已分配: {allocated:.2f}GB | 缓存: {reserved:.2f}GB")
                print(f"      峰值: {max_allocated:.2f}GB | 总计: {total_memory:.1f}GB")
                print(f"      利用率: {allocated/total_memory*100:.1f}%")

                # 重置峰值内存统计
                torch.cuda.reset_peak_memory_stats()

            # 系统资源状态
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            print(f"   💻 系统状态:")
            print(f"      CPU使用率: {cpu_percent:.1f}%")
            print(f"      内存使用: {memory.used/1e9:.1f}GB/{memory.total/1e9:.1f}GB ({memory.percent:.1f}%)")

            # 训练进度和预估
            elapsed_total = time.time() - training_start_time
            avg_epoch_time = elapsed_total / (epoch + 1)
            eta_total = avg_epoch_time * (epochs - epoch - 1)

            print(f"   📈 训练进度:")
            print(f"      已完成: {epoch+1}/{epochs} 轮次 ({(epoch+1)/epochs*100:.1f}%)")
            print(f"      已处理: {total_samples_processed:,} 样本")
            print(f"      总耗时: {elapsed_total/60:.1f}分钟")
            print(f"      预计剩余: {eta_total/60:.1f}分钟")

        # 训练完成总结
        total_training_time = time.time() - training_start_time
        avg_samples_per_sec = total_samples_processed / total_training_time

        print(f"\n{'='*80}")
        print(f"🎉 优化训练完成！")
        print(f"{'='*80}")
        print(f"🏆 最佳结果:")
        print(f"   最佳MAE: {best_mae:.3f}岁 (轮次 {best_epoch})")
        print(f"   总训练时间: {total_training_time/60:.1f}分钟")
        print(f"   总处理样本: {total_samples_processed:,}")
        print(f"   平均训练速度: {avg_samples_per_sec:.1f} 样本/秒")
        print(f"   模型已保存: best_optimized_age_model.pth")

        # 生成训练报告
        self._generate_training_report(best_mae, best_epoch, total_training_time)

    def _generate_training_report(self, best_mae, best_epoch, total_time):
        """生成详细的训练报告"""
        print(f"\n📋 详细训练报告:")
        print(f"   最佳轮次: {best_epoch}")
        print(f"   平均轮次时间: {np.mean(self.epoch_times):.1f}秒")
        print(f"   最快轮次时间: {np.min(self.epoch_times):.1f}秒")
        print(f"   最慢轮次时间: {np.max(self.epoch_times):.1f}秒")
        print(f"   平均训练速度: {np.mean(self.samples_per_second):.1f} 样本/秒")
        print(f"   最快训练速度: {np.max(self.samples_per_second):.1f} 样本/秒")
        print(f"   MAE改善幅度: {self.val_maes[0] - best_mae:.3f}岁")
        print(f"   训练效率: {len(self.train_losses) / (total_time/3600):.1f} 轮次/小时")

def main():
    print("=" * 80)
    print("🚀 GPU内存优化年龄识别训练系统")
    print("=" * 80)

    # 详细的系统检查
    print("🔍 系统环境检查:")
    print(f"   Python版本: {sys.version}")
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {'✅' if torch.cuda.is_available() else '❌'}")

    if torch.cuda.is_available():
        gpu_props = torch.cuda.get_device_properties(0)
        print(f"   GPU型号: {gpu_props.name}")
        print(f"   GPU内存: {gpu_props.total_memory/1e9:.1f}GB")
        print(f"   CUDA版本: {torch.version.cuda}")
        print(f"   cuDNN版本: {torch.backends.cudnn.version()}")

    # 检查数据集
    print(f"\n📁 数据集检查:")
    if not os.path.exists('data/train_annotations.json'):
        print("❌ 训练数据集不存在，请先运行数据预处理脚本")
        return
    if not os.path.exists('data/val_annotations.json'):
        print("❌ 验证数据集不存在，请先运行数据预处理脚本")
        return
    print("✅ 数据集文件存在")
    
    # 数据预处理 - 增强版
    train_transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.RandomCrop((224, 224)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(20),
        transforms.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.3, hue=0.1),
        transforms.RandomGrayscale(p=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    print(f"\n📊 加载数据集:")
    train_dataset = AgeDataset('data/train', 'data/train_annotations.json', train_transform)
    val_dataset = AgeDataset('data/val', 'data/val_annotations.json', val_transform)

    print(f"   训练集大小: {len(train_dataset):,} 张图片")
    print(f"   验证集大小: {len(val_dataset):,} 张图片")

    # 优化的批次大小 - 基于内存分析结果
    batch_size = 256  # 从分析结果得出的最优批次大小
    print(f"\n⚙️ 数据加载配置:")
    print(f"   批次大小: {batch_size} (基于GPU内存优化)")

    # 数据加载器 - 优化版
    num_workers = 4 if os.name != 'nt' else 0
    print(f"   工作进程: {num_workers}")
    print(f"   内存固定: {'✅' if torch.cuda.is_available() else '❌'}")

    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        persistent_workers=num_workers > 0
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        persistent_workers=num_workers > 0
    )

    print(f"   训练批次数: {len(train_loader)}")
    print(f"   验证批次数: {len(val_loader)}")

    # 创建优化模型
    print(f"\n🧠 模型初始化:")
    model = OptimizedCNN()
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    model_size_mb = total_params * 4 / 1e6

    print(f"   模型架构: OptimizedCNN")
    print(f"   总参数: {total_params:,}")
    print(f"   可训练参数: {trainable_params:,}")
    print(f"   模型大小: {model_size_mb:.1f}MB")

    # 预估GPU内存使用
    if torch.cuda.is_available():
        estimated_memory = (model_size_mb + batch_size * 3 * 224 * 224 * 4 / 1e6 * 2) / 1024  # GB
        gpu_total = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"   预估GPU内存: {estimated_memory:.1f}GB / {gpu_total:.1f}GB ({estimated_memory/gpu_total*100:.1f}%)")

    # 创建训练器
    trainer = OptimizedTrainer(model, learning_rate=0.001, use_amp=True)

    # 开始训练
    epochs = 30
    print(f"\n🎯 训练配置:")
    print(f"   训练轮次: {epochs}")
    print(f"   预计总样本: {len(train_dataset) * epochs:,}")
    print(f"   预计总批次: {len(train_loader) * epochs:,}")

    trainer.train(train_loader, val_loader, epochs=epochs)

    print(f"\n🎉 训练系统完成！")
    print(f"   最佳模型: best_optimized_age_model.pth")
    print(f"   训练日志: 已显示在控制台")
    print("=" * 80)

if __name__ == "__main__":
    main()
