#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量视频年龄识别处理脚本
支持批量处理多个视频文件
"""

import os
import glob
import argparse
import time
from datetime import datetime
import json
from video_age_recognition import VideoAgeRecognizer

class BatchVideoProcessor:
    """批量视频处理器"""
    
    def __init__(self, model_path, model_type='simple', output_dir='output'):
        """
        初始化批量处理器
        
        Args:
            model_path: 模型文件路径
            model_type: 模型类型
            output_dir: 输出目录
        """
        self.recognizer = VideoAgeRecognizer(model_path, model_type)
        self.output_dir = output_dir
        self.processing_log = []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"✅ 批量处理器初始化完成")
        print(f"   输出目录: {output_dir}")
    
    def process_video_file(self, video_path):
        """处理单个视频文件"""
        print(f"\n🎬 处理视频: {video_path}")
        
        # 生成输出文件名
        video_name = os.path.basename(video_path)
        name_without_ext = os.path.splitext(video_name)[0]
        output_path = os.path.join(self.output_dir, f"{name_without_ext}_age_detected.mp4")
        
        start_time = time.time()
        
        try:
            # 重置统计信息
            self.recognizer.frame_count = 0
            self.recognizer.detection_count = 0
            self.recognizer.processing_times = []
            
            # 处理视频
            self.recognizer.process_video(
                video_path=video_path,
                output_path=output_path,
                show_preview=False  # 批量处理时不显示预览
            )
            
            processing_time = time.time() - start_time
            
            # 记录处理结果
            result = {
                'input_file': video_path,
                'output_file': output_path,
                'processing_time': processing_time,
                'frames_processed': self.recognizer.frame_count,
                'faces_detected': self.recognizer.detection_count,
                'avg_fps': self.recognizer.frame_count / processing_time if processing_time > 0 else 0,
                'status': 'success',
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"✅ 处理完成: {video_name}")
            print(f"   输出文件: {output_path}")
            print(f"   处理时间: {processing_time:.1f}秒")
            print(f"   检测人脸: {self.recognizer.detection_count}")
            
        except Exception as e:
            result = {
                'input_file': video_path,
                'output_file': None,
                'processing_time': time.time() - start_time,
                'frames_processed': 0,
                'faces_detected': 0,
                'avg_fps': 0,
                'status': 'error',
                'error_message': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"❌ 处理失败: {video_name}")
            print(f"   错误信息: {e}")
        
        self.processing_log.append(result)
        return result
    
    def process_directory(self, input_dir, file_patterns=['*.mp4', '*.avi', '*.mov', '*.mkv']):
        """处理目录中的所有视频文件"""
        print(f"\n📁 扫描目录: {input_dir}")
        
        # 查找视频文件
        video_files = []
        for pattern in file_patterns:
            pattern_path = os.path.join(input_dir, pattern)
            video_files.extend(glob.glob(pattern_path))
        
        if not video_files:
            print("❌ 未找到视频文件")
            return
        
        print(f"📋 找到 {len(video_files)} 个视频文件:")
        for i, video_file in enumerate(video_files, 1):
            print(f"   {i}. {os.path.basename(video_file)}")
        
        # 批量处理
        total_start_time = time.time()
        successful = 0
        failed = 0
        
        for i, video_file in enumerate(video_files, 1):
            print(f"\n{'='*60}")
            print(f"进度: {i}/{len(video_files)}")
            print(f"{'='*60}")
            
            result = self.process_video_file(video_file)
            
            if result['status'] == 'success':
                successful += 1
            else:
                failed += 1
        
        # 总结
        total_time = time.time() - total_start_time
        
        print(f"\n{'='*60}")
        print(f"🎉 批量处理完成!")
        print(f"{'='*60}")
        print(f"总文件数: {len(video_files)}")
        print(f"成功处理: {successful}")
        print(f"处理失败: {failed}")
        print(f"总耗时: {total_time/60:.1f}分钟")
        print(f"平均每文件: {total_time/len(video_files):.1f}秒")
        
        # 保存处理日志
        self.save_processing_log()
    
    def process_file_list(self, file_list):
        """处理文件列表"""
        print(f"\n📋 处理文件列表 ({len(file_list)} 个文件)")
        
        total_start_time = time.time()
        successful = 0
        failed = 0
        
        for i, video_file in enumerate(file_list, 1):
            if not os.path.exists(video_file):
                print(f"⚠️ 文件不存在: {video_file}")
                continue
            
            print(f"\n{'='*60}")
            print(f"进度: {i}/{len(file_list)}")
            print(f"{'='*60}")
            
            result = self.process_video_file(video_file)
            
            if result['status'] == 'success':
                successful += 1
            else:
                failed += 1
        
        # 总结
        total_time = time.time() - total_start_time
        
        print(f"\n{'='*60}")
        print(f"🎉 批量处理完成!")
        print(f"{'='*60}")
        print(f"总文件数: {len(file_list)}")
        print(f"成功处理: {successful}")
        print(f"处理失败: {failed}")
        print(f"总耗时: {total_time/60:.1f}分钟")
        
        # 保存处理日志
        self.save_processing_log()
    
    def save_processing_log(self):
        """保存处理日志"""
        log_file = os.path.join(self.output_dir, 'processing_log.json')
        
        log_data = {
            'batch_info': {
                'total_files': len(self.processing_log),
                'successful': len([r for r in self.processing_log if r['status'] == 'success']),
                'failed': len([r for r in self.processing_log if r['status'] == 'error']),
                'total_processing_time': sum(r['processing_time'] for r in self.processing_log),
                'total_frames': sum(r['frames_processed'] for r in self.processing_log),
                'total_detections': sum(r['faces_detected'] for r in self.processing_log),
                'batch_timestamp': datetime.now().isoformat()
            },
            'file_results': self.processing_log
        }
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)
        
        print(f"📋 处理日志已保存: {log_file}")
    
    def generate_report(self):
        """生成处理报告"""
        if not self.processing_log:
            print("📋 暂无处理记录")
            return
        
        successful_files = [r for r in self.processing_log if r['status'] == 'success']
        failed_files = [r for r in self.processing_log if r['status'] == 'error']
        
        print(f"\n📊 处理报告:")
        print(f"   总文件数: {len(self.processing_log)}")
        print(f"   成功处理: {len(successful_files)}")
        print(f"   处理失败: {len(failed_files)}")
        
        if successful_files:
            total_time = sum(r['processing_time'] for r in successful_files)
            total_frames = sum(r['frames_processed'] for r in successful_files)
            total_detections = sum(r['faces_detected'] for r in successful_files)
            avg_fps = sum(r['avg_fps'] for r in successful_files) / len(successful_files)
            
            print(f"   总处理时间: {total_time/60:.1f}分钟")
            print(f"   总处理帧数: {total_frames:,}")
            print(f"   总检测人脸: {total_detections:,}")
            print(f"   平均处理速度: {avg_fps:.1f} FPS")
        
        if failed_files:
            print(f"\n❌ 失败文件:")
            for result in failed_files:
                print(f"   {os.path.basename(result['input_file'])}: {result.get('error_message', '未知错误')}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='批量视频年龄识别处理')
    
    # 必需参数
    parser.add_argument('--model', type=str, required=True,
                       help='训练好的模型路径')
    
    # 输入源 (三选一)
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--input-dir', type=str, help='输入视频目录')
    group.add_argument('--file-list', type=str, nargs='+', help='视频文件列表')
    group.add_argument('--list-file', type=str, help='包含视频路径的文本文件')
    
    # 可选参数
    parser.add_argument('--output-dir', type=str, default='batch_output',
                       help='输出目录 (默认: batch_output)')
    parser.add_argument('--model-type', choices=['simple', 'advanced'], default='simple',
                       help='模型类型 (默认: simple)')
    parser.add_argument('--patterns', type=str, nargs='+', 
                       default=['*.mp4', '*.avi', '*.mov', '*.mkv'],
                       help='视频文件匹配模式')
    
    return parser.parse_args()

def main():
    args = parse_args()
    
    print("=" * 80)
    print("🎬 批量视频年龄识别处理系统")
    print("=" * 80)
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    try:
        # 创建批量处理器
        processor = BatchVideoProcessor(
            model_path=args.model,
            model_type=args.model_type,
            output_dir=args.output_dir
        )
        
        # 根据输入类型处理
        if args.input_dir:
            if not os.path.exists(args.input_dir):
                print(f"❌ 输入目录不存在: {args.input_dir}")
                return
            processor.process_directory(args.input_dir, args.patterns)
        
        elif args.file_list:
            processor.process_file_list(args.file_list)
        
        elif args.list_file:
            if not os.path.exists(args.list_file):
                print(f"❌ 文件列表不存在: {args.list_file}")
                return
            
            with open(args.list_file, 'r', encoding='utf-8') as f:
                file_list = [line.strip() for line in f if line.strip()]
            
            processor.process_file_list(file_list)
        
        # 生成最终报告
        processor.generate_report()
    
    except Exception as e:
        print(f"❌ 批量处理过程中出现错误: {e}")
        return
    
    print(f"\n🎉 批量处理完成!")
    print(f"输出目录: {args.output_dir}")

if __name__ == "__main__":
    main()
