import torch
from torchvision import transforms
from PIL import Image
import os
import argparse
import time
from train_age_model import AgeRecognitionModel

def predict_age(model_path, image_path, model_name='resnet50'):
    """
    使用训练好的模型预测图片中人物的年龄
    """
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        return None
    
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在: {image_path}")
        return None
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型
    try:
        model = AgeRecognitionModel(model_name=model_name)
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.to(device)
        model.eval()
        print(f"模型加载成功: {model_path}")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return None
    
    # 图像预处理
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 加载并处理图像
    try:
        image = Image.open(image_path).convert('RGB')
        input_tensor = transform(image).unsqueeze(0).to(device)
    except Exception as e:
        print(f"图像处理失败: {e}")
        return None
    
    # 预测
    with torch.no_grad():
        start_time = time.time()
        output = model(input_tensor)
        inference_time = time.time() - start_time
    
    # 获取预测结果
    predicted_age = max(0, output.item())  # 确保年龄非负
    
    print(f"预测年龄: {predicted_age:.1f}岁")
    print(f"推理时间: {inference_time*1000:.1f}毫秒")
    
    return predicted_age

def main():
    parser = argparse.ArgumentParser(description='年龄预测')
    parser.add_argument('--model', type=str, default='best_age_model.pth', help='模型路径')
    parser.add_argument('--image', type=str, required=True, help='图片路径')
    parser.add_argument('--model_name', type=str, default='resnet50', 
                        choices=['resnet18', 'resnet34', 'resnet50', 'efficientnet_b0'], 
                        help='模型架构')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("年龄识别预测程序")
    print("=" * 60)
    
    predict_age(args.model, args.image, args.model_name)

if __name__ == '__main__':
    main()